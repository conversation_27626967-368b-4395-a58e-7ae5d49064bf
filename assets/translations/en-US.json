{"app": {"name": "NewLife", "title": "Flutter Base"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "email_hint": "Enter email", "password_hint": "Enter password", "forgot_password": "Forgot password?", "or_divider": "------- or -------", "login_success": "Login successful!", "logout_success": "Logout successful!", "email_required": "Please enter email", "password_required": "Please enter password", "login_failed": "<PERSON><PERSON> failed", "logout_failed": "Logout failed"}, "home": {"title": "Home", "welcome": "Welcome to Flutter Base!", "description": "This app has been fully integrated with theme management features:", "features": {"light_mode": "🌞 Light Mode", "dark_mode": "🌙 Dark Mode", "system_detection": "⚙️ System Theme Detection", "persistence": "💾 Theme Persistence", "real_time": "🔄 Real-time Theme Switching"}, "theme_instruction": "Use the 🌙/☀️ button on AppBar to switch themes"}, "common": {"notification": "Notification", "close": "Close", "cancel": "Cancel", "exit": "Exit", "error": "An error occurred", "success": "Success", "loading": "Loading...", "retry": "Retry", "confirm": "Confirm"}, "error": {"network": "Network connection error", "timeout": "Request timeout", "timeout_connection": "Connection timeout", "server": "Server error", "authentication": "Authentication error", "authorization": "Access denied", "validation": "Invalid data", "unknown": "Unknown error", "request_cancelled": "Request was cancelled", "general": "An unknown error occurred", "token_expired": "Session has expired", "token_refresh_failed": "Failed to refresh session", "session_expired": "Session has expired, please login again"}, "dialog": {"exit_app_title": "Exit App", "exit_app_message": "Do you want to exit the application?", "create_account": "Create Account", "register_coming_soon": "Register functionality coming soon!"}, "navigation": {"error_title": "Navigation Error", "error_message": "Failed to navigate to: {path}", "error_detail": "Error: {error}", "go_home": "Go to Home"}, "language": {"title": "Choose Language", "vietnamese": "Tiếng <PERSON>", "english": "English", "tooltip": "Change language"}}