import 'package:package_info_plus/package_info_plus.dart';

class AppConfig {
  static String? _environment;
  static PackageInfo? _packageInfo;

  static Future<void> initialize() async {
    _packageInfo = await PackageInfo.fromPlatform();
    _environment = _detectEnvironment();
  }

  static String _detectEnvironment() {
    if (_packageInfo == null) return 'production';
    final packageName = _packageInfo!.packageName;
    if (packageName.endsWith('.dev')) {
      return 'dev';
    } else if (packageName.endsWith('.staging')) {
      return 'staging';
    }
    return 'production';
  }

  static String get environment => _environment ?? 'production';
  static String get appName {
    switch (environment) {
      case 'dev':
        return 'Flutter Base Dev';
      case 'staging':
        return 'Flutter Base Staging';
      default:
        return 'Flutter Base';
    }
  }

  static String get baseUrl {
    switch (environment) {
      case 'dev':
        return 'https://api-dev.example.com';
      case 'staging':
        return 'https://api-staging.example.com';
      default:
        return 'https://api.example.com';
    }
  }

  static String get apiKey {
    switch (environment) {
      case 'dev':
        return 'dev_api_key_here';
      case 'staging':
        return 'staging_api_key_here';
      default:
        return 'production_api_key_here';
    }
  }

  static bool get isDebug {
    switch (environment) {
      case 'dev':
        return true;
      case 'staging':
        return false;
      default:
        return false;
    }
  }

  static int get timeoutDuration {
    switch (environment) {
      case 'dev':
        return 60000;
      case 'staging':
        return 45000;
      default:
        return 30000;
    }
  }

  static bool get isDevelopment => environment == 'dev';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';

  static String get environmentName {
    switch (environment) {
      case 'dev':
        return 'Development';
      case 'staging':
        return 'Staging';
      default:
        return 'Production';
    }
  }
}
