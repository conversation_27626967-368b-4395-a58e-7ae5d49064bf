import 'package:shared_preferences/shared_preferences.dart';

class Prefs {
  // User preferences
  static const String email = 'user_email';
  static const String rememberPassword = 'remember_password';
  static const String theme = 'theme_preference';
  static const String language = 'language_preference';
  // Token storage
  static const String accessToken = 'access_token';

  static Prefs? _instance;
  static Prefs get instance => _instance ??= Prefs._();

  Prefs._();

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  Future<T?> get<T>(String key) async {
    await init();
    final value = _prefs!.get(key);
    if (value is T) return value;
    if (T == DateTime && value is String) {
      return DateTime.tryParse(value) as T?;
    }
    return null;
  }

  Future<void> set(String key, dynamic value) async {
    await init();
    if (value is String) {
      await _prefs!.setString(key, value);
    } else if (value is bool) {
      await _prefs!.setBool(key, value);
    } else if (value is int) {
      await _prefs!.setInt(key, value);
    } else if (value is double) {
      await _prefs!.setDouble(key, value);
    } else if (value is DateTime) {
      await _prefs!.setString(key, value.toIso8601String());
    } else if (value is List<String>) {
      await _prefs!.setStringList(key, value);
    }
  }

  Future<void> remove(String key) async {
    await init();
    await _prefs!.remove(key);
  }

  Future<void> clearAuth() async {
    await Future.wait([
      remove(rememberPassword),
      remove(accessToken),
    ]);
  }

  Future<void> clearTokens() async {
    await remove(accessToken);
  }

  Future<bool> hasToken() async {
    final token = await get<String>(accessToken);
    return token != null && token.isNotEmpty;
  }

  Future<String?> getAuthorizationHeader() async {
    final token = await get<String>(accessToken);

    if (token != null && token.isNotEmpty) {
      return 'Bearer $token';
    }

    return null;
  }

  Future<void> clearAll() async {
    await init();
    await _prefs!.clear();
  }
}


