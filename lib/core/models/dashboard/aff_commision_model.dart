class AffCommissionModel {
  double? affCommission;
  double? sales;
  double? totalOrder;

  AffCommissionModel({this.totalOrder, this.sales, this.affCommission});

  AffCommissionModel.fromJson(Map<String, dynamic> json) {
    totalOrder = double.tryParse((json['total_order']??0).toString());
    sales = double.tryParse((json['sales']??0).toString());
    affCommission = double.tryParse((json['aff_commission']??0).toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['aff_commission'] = affCommission;
    data['sales'] = sales;
    data['total_order'] = totalOrder;
    return data;
  }
}
