import '../../config/app_config.dart';

class ApiConfig {
  ApiConfig._();

  static const String apiVersion = 'v1';
  
  static String get baseUrl {
    switch (AppConfig.environment) {
      case 'dev':
        return 'https://qa1.arc-dev.org';
      case 'staging':
        return 'https://staging.arc-dev.org';
      case 'production':
        return 'https://api.arc-dev.org';
      default:
        return 'https://qa1.arc-dev.org';
    }
  }

  static String get apiBaseUrl => '$baseUrl/$apiVersion';

  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 1);

  static const Map<String, String> defaultHeaders = {
    'accept': 'application/json',
    'Content-Type': 'application/json',
  };
}

class ApiEndpoints {
  ApiEndpoints._();

  static const String auth = '/auth';
  static const String login = '$auth/login/';
  static const String logout = '$auth/logout/';
  static const String refreshToken = '$auth/refresh/';
  static const String register = '$auth/register/';
  static const String forgotPassword = '$auth/forgot-password/';
  static const String resetPassword = '$auth/reset-password/';

}

enum ApiEnvironment {
  dev,
  staging,
  production,
}

extension ApiEnvironmentExtension on ApiEnvironment {
  String get name {
    switch (this) {
      case ApiEnvironment.dev:
        return 'dev';
      case ApiEnvironment.staging:
        return 'staging';
      case ApiEnvironment.production:
        return 'production';
    }
  }

  String get baseUrl {
    switch (this) {
      case ApiEnvironment.dev:
        return 'https://qa1.arc-dev.org';
      case ApiEnvironment.staging:
        return 'https://staging.arc-dev.org';
      case ApiEnvironment.production:
        return 'https://api.arc-dev.org';
    }
  }
}
