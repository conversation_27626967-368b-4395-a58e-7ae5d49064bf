import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final ApiError? error;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.error,
  });

  factory ApiResponse.success({
    required T data,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error({
    required ApiError error,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      error: error,
      message: message,
      statusCode: statusCode,
    );
  }
}

class ApiError {
  final ApiErrorType type;
  final String message;
  final int? statusCode;
  final dynamic originalError;

  const ApiError({
    required this.type,
    required this.message,
    this.statusCode,
    this.originalError,
  });

  factory ApiError.network({String? message}) {
    return ApiError(
      type: ApiErrorType.network,
      message: message ?? 'error.network'.tr(),
    );
  }

  factory ApiError.timeout({String? message}) {
    return ApiError(
      type: ApiErrorType.timeout,
      message: message ?? 'error.timeout'.tr(),
    );
  }

  factory ApiError.server({
    required int statusCode,
    String? message,
    dynamic originalError,
  }) {
    return ApiError(
      type: ApiErrorType.server,
      message: message ?? 'error.server'.tr(),
      statusCode: statusCode,
      originalError: originalError,
    );
  }

  factory ApiError.authentication({String? message}) {
    return ApiError(
      type: ApiErrorType.authentication,
      message: message ?? 'error.authentication'.tr(),
      statusCode: 401,
    );
  }

  factory ApiError.authorization({String? message}) {
    return ApiError(
      type: ApiErrorType.authorization,
      message: message ?? 'error.authorization'.tr(),
      statusCode: 403,
    );
  }

  factory ApiError.validation({
    String? message,
    dynamic originalError,
  }) {
    return ApiError(
      type: ApiErrorType.validation,
      message: message ?? 'error.validation'.tr(),
      statusCode: 422,
      originalError: originalError,
    );
  }

  factory ApiError.unknown({
    String? message,
    dynamic originalError,
  }) {
    return ApiError(
      type: ApiErrorType.unknown,
      message: message ?? 'error.unknown'.tr(),
      originalError: originalError,
    );
  }

  factory ApiError.fromDioException(DioException exception) {
    switch (exception.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ApiError.timeout(message: 'error.timeout_connection'.tr());

      case DioExceptionType.connectionError:
        return ApiError.network(message: 'error.network'.tr());
      
      case DioExceptionType.badResponse:
        final statusCode = exception.response?.statusCode ?? 0;
        final message = _extractErrorMessage(exception.response?.data);
        
        if (statusCode == 401) {
          return ApiError.authentication(message: message);
        } else if (statusCode == 403) {
          return ApiError.authorization(message: message);
        } else if (statusCode == 422) {
          return ApiError.validation(
            message: message,
            originalError: exception.response?.data,
          );
        } else {
          return ApiError.server(
            statusCode: statusCode,
            message: message,
            originalError: exception.response?.data,
          );
        }
      
      case DioExceptionType.cancel:
        return ApiError.unknown(message: 'error.request_cancelled'.tr());

      case DioExceptionType.unknown:
      default:
        return ApiError.unknown(
          message: exception.message ?? 'error.unknown'.tr(),
          originalError: exception,
        );
    }
  }

  static String _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return 'error.server'.tr();

    if (responseData is Map<String, dynamic>) {
      return responseData['message']?.toString() ??
             responseData['error']?.toString() ??
             responseData['detail']?.toString() ??
             'error.server'.tr();
    }

    if (responseData is String) {
      return responseData;
    }

    return 'error.server'.tr();
  }
}

enum ApiErrorType {
  network,
  timeout,
  server,
  authentication,
  authorization,
  validation,
  unknown,
}

class LoginRequest {
  final String url;
  final String email;
  final String password;

  const LoginRequest({
    required this.url,
    required this.email,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'email': email,
      'password': password,
    };
  }
}

class LoginResponse {
  final String accessToken;

  const LoginResponse({
    required this.accessToken,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      accessToken: json['access_token'] ?? json['accessToken'] ?? '',
    );
  }
}
