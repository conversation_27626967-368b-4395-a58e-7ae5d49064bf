import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';
import '../preferences/user_preferences_manager.dart';
import '../routing/route_names.dart';
import '../utils/dialog_utils.dart';

class AuthInterceptor extends Interceptor {
  final Prefs _prefs = Prefs.instance;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final authHeader = await _prefs.getAuthorizationHeader();
    if (authHeader != null) {
      options.headers['Authorization'] = authHeader;
    }

    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Clear tokens and handle session expiry
      await _prefs.clearTokens();

      // Get context from request options if available
      final context = err.requestOptions.extra['context'] as BuildContext?;

      if (context != null && context.mounted) {
        // Show session expired dialog and redirect to login
        DialogUtils.showDialogMessage(
          'error.session_expired'.tr(),
          context: context,
          action: () {
            context.go(RoutePaths.login);
          },
        );
      }
    }

    super.onError(err, handler);
  }
}

class LoggingInterceptor extends Interceptor {
  final bool logRequest;
  final bool logResponse;
  final bool logError;

  LoggingInterceptor({
    this.logRequest = true,
    this.logResponse = true,
    this.logError = true,
  });

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (logRequest) {
      _logRequest(options);
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (logResponse) {
      _logResponse(response);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (logError) {
      _logError(err);
    }
    super.onError(err, handler);
  }

  void _logRequest(RequestOptions options) {
    // Only log in debug mode
    if (kDebugMode) {
      final curlCommand = _generateCurlCommand(options);

      // ANSI color codes for terminal
      const String green = '\x1B[32m';
      const String blue = '\x1B[34m';
      const String yellow = '\x1B[33m';
      const String reset = '\x1B[0m';

      // Log with clear separators for easy copying
      print('$green${'=' * 80}$reset');
      print('$blue🚀 CURL COMMAND$reset');
      print('$green${'=' * 80}$reset');
      print('$yellow$curlCommand$reset');
      print('$green${'=' * 80}$reset');
    }
  }

  String _generateCurlCommand(RequestOptions options) {
    final buffer = StringBuffer();

    // Start with curl command
    buffer.write('curl -X ${options.method.toUpperCase()}');

    // Add URL
    buffer.write(' "${options.uri}"');

    // Add headers
    options.headers.forEach((key, value) {
      if (value != null) {
        buffer.write(' \\  -H "$key: $value"');
      }
    });

    // Add data for POST/PUT/PATCH requests
    if (options.data != null &&
        ['POST', 'PUT', 'PATCH'].contains(options.method.toUpperCase())) {

      String dataString;
      if (options.data is Map) {
        // Convert Map to JSON string
        dataString = _mapToJsonString(options.data as Map<String, dynamic>);
      } else if (options.data is String) {
        dataString = options.data as String;
      } else {
        dataString = options.data.toString();
      }

      buffer.write(' \\  -d \'$dataString\'');
    }

    // Add content type if not already specified
    if (!options.headers.containsKey('Content-Type') &&
        ['POST', 'PUT', 'PATCH'].contains(options.method.toUpperCase())) {
      buffer.write(' \\  -H "Content-Type: application/json"');
    }

    return buffer.toString();
  }

  String _mapToJsonString(Map<String, dynamic> map) {
    final buffer = StringBuffer();
    buffer.write('{');

    final entries = map.entries.toList();
    for (int i = 0; i < entries.length; i++) {
      final entry = entries[i];
      final key = entry.key;
      final value = entry.value;

      buffer.write('"$key":');

      if (value is String) {
        buffer.write('"$value"');
      } else if (value is num || value is bool) {
        buffer.write(value.toString());
      } else if (value == null) {
        buffer.write('null');
      } else {
        buffer.write('"${value.toString()}"');
      }

      if (i < entries.length - 1) {
        buffer.write(',');
      }
    }

    buffer.write('}');
    return buffer.toString();
  }

  void _logResponse(Response response) {
    if (kDebugMode) {
      // ANSI color codes
      const String green = '\x1B[32m';
      const String blue = '\x1B[34m';
      const String cyan = '\x1B[36m';
      const String reset = '\x1B[0m';

      final responseData = _formatResponseData(response.data);

      print('$green${'=' * 80}$reset');
      print('$blue✅ API RESPONSE$reset');
      print('$green${'=' * 80}$reset');
      print('$cyan${response.statusCode} ${response.requestOptions.method} ${response.requestOptions.uri}$reset');
      print('$cyan$responseData$reset');
      print('$green${'=' * 80}$reset');
    }
  }

  void _logError(DioException err) {
    if (kDebugMode) {
      // ANSI color codes
      const String red = '\x1B[31m';
      const String yellow = '\x1B[33m';
      const String reset = '\x1B[0m';

      print('$red${'=' * 80}$reset');
      print('$red❌ API ERROR$reset');
      print('$red${'=' * 80}$reset');
      print('$yellow${err.response}');
      print('$red${'=' * 80}$reset');
    }
  }

  String _formatResponseData(dynamic data) {
    if (data == null) return 'No data';

    if (data is Map || data is List) {
      final jsonString = data.toString();
      // Truncate if too long
      if (jsonString.length > 500) {
        return '${jsonString.substring(0, 500)}... (truncated)';
      }
      return jsonString;
    }

    return data.toString();
  }
}

class RetryInterceptor extends Interceptor {
  final int maxRetries;
  final Duration retryDelay;
  final List<int> retryStatusCodes;

  RetryInterceptor({
    this.maxRetries = 3,
    this.retryDelay = const Duration(seconds: 1),
    this.retryStatusCodes = const [500, 502, 503, 504],
  });

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final shouldRetry = _shouldRetry(err);
    final retryCount = err.requestOptions.extra['retry_count'] as int? ?? 0;

    if (shouldRetry && retryCount < maxRetries) {
      err.requestOptions.extra['retry_count'] = retryCount + 1;
      
      await Future.delayed(_calculateDelay(retryCount));
      
      try {
        final dio = Dio();
        final response = await dio.fetch(err.requestOptions);
        return handler.resolve(response);
      } catch (e) {
        return handler.next(err);
      }
    }

    super.onError(err, handler);
  }

  bool _shouldRetry(DioException err) {
    if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionError) {
      return true;
    }

    if (err.response?.statusCode != null) {
      return retryStatusCodes.contains(err.response!.statusCode);
    }

    return false;
  }

  Duration _calculateDelay(int retryCount) {
    return Duration(
      milliseconds: (retryDelay.inMilliseconds * (1 << retryCount)).clamp(
        retryDelay.inMilliseconds,
        30000,
      ),
    );
  }
}
