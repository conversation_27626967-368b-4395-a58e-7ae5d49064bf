import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../core/utils/dialog_utils.dart';
import 'api_config.dart';
import 'api_response.dart';
import 'api_interceptors.dart';

enum HttpMethod { get, post, put, delete, patch }

class ApiService {
  static ApiService? _instance;
  static ApiService get instance => _instance ??= ApiService._();
  
  late final Dio _dio;

  ApiService._() {
    _dio = Dio();
    _setupDio();
  }

  void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: ApiConfig.apiBaseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      sendTimeout: ApiConfig.sendTimeout,
      headers: ApiConfig.defaultHeaders,
    );

    _dio.interceptors.addAll([
      AuthInterceptor(),
      RetryInterceptor(
        maxRetries: ApiConfig.maxRetries,
        retryDelay: ApiConfig.retryDelay,
      ),
      LoggingInterceptor(),
    ]);
  }

  Future<ApiResponse<T>> request<T>({
    required String endpoint,
    required HttpMethod method,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
    T Function(dynamic)? parser,
    bool showLoading = true,
    BuildContext? context,
    Function(T)? onSuccess,
    Function(ApiError)? onError,
  }) async {
    BuildContext? activeContext = context ?? DialogUtils.context;
    
    try {
      if (showLoading && activeContext?.mounted == true) {
        activeContext!.loaderOverlay.show();
      }

      final options = Options(
        method: method.name.toUpperCase(),
        headers: headers,
        extra: {'context': activeContext}, // Pass context for interceptor
      );

      final Response response;
      switch (method) {
        case HttpMethod.get:
          response = await _dio.get(
            endpoint,
            queryParameters: queryParameters,
            options: options,
          );
          break;
        case HttpMethod.post:
          response = await _dio.post(
            endpoint,
            data: data,
            queryParameters: queryParameters,
            options: options,
          );
          break;
        case HttpMethod.put:
          response = await _dio.put(
            endpoint,
            data: data,
            queryParameters: queryParameters,
            options: options,
          );
          break;
        case HttpMethod.patch:
          response = await _dio.patch(
            endpoint,
            data: data,
            queryParameters: queryParameters,
            options: options,
          );
          break;
        case HttpMethod.delete:
          response = await _dio.delete(
            endpoint,
            data: data,
            queryParameters: queryParameters,
            options: options,
          );
          break;
      }

      if (showLoading && activeContext?.mounted == true) {
        activeContext!.loaderOverlay.hide();
      }

      final parsedData = parser != null ? parser(response.data) : response.data as T;

      final apiResponse = ApiResponse<T>.success(
        data: parsedData,
        statusCode: response.statusCode,
        message: 'Success',
      );

      if (onSuccess != null && parsedData != null) {
        onSuccess(parsedData);
      }

      return apiResponse;

    } on DioException catch (e) {
      if (showLoading && activeContext?.mounted == true) {
        activeContext!.loaderOverlay.hide();
      }

      final apiError = ApiError.fromDioException(e);

      if (onError != null) {
        onError(apiError);
      }

      return ApiResponse<T>.error(
        error: apiError,
        statusCode: e.response?.statusCode,
        message: apiError.message,
      );
    } catch (e) {
      if (showLoading && activeContext?.mounted == true) {
        activeContext!.loaderOverlay.hide();
      }

      final apiError = ApiError.unknown(
        message: '${'error.general'.tr()}: ${e.toString()}',
        originalError: e,
      );

      if (onError != null) {
        onError(apiError);
      }

      return ApiResponse<T>.error(
        error: apiError,
        message: apiError.message,
      );
    }
  }

  Future<ApiResponse<T>> get<T>({
    required String endpoint,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? parser,
    BuildContext? context,
    Function(T)? onSuccess,
    Function(ApiError)? onError,
  }) async {
    return request<T>(
      endpoint: endpoint,
      method: HttpMethod.get,
      queryParameters: queryParameters,
      parser: parser,
      context: context,
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  Future<ApiResponse<T>> post<T>({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? parser,
    BuildContext? context,
    Function(T)? onSuccess,
    Function(ApiError)? onError,
  }) async {
    return request<T>(
      endpoint: endpoint,
      method: HttpMethod.post,
      data: data,
      queryParameters: queryParameters,
      parser: parser,
      context: context,
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  Future<ApiResponse<T>> put<T>({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? parser,
    BuildContext? context,
    Function(T)? onSuccess,
    Function(ApiError)? onError,
  }) async {
    return request<T>(
      endpoint: endpoint,
      method: HttpMethod.put,
      data: data,
      queryParameters: queryParameters,
      parser: parser,
      context: context,
      onSuccess: onSuccess,
      onError: onError,
    );
  }

  Future<ApiResponse<T>> delete<T>({
    required String endpoint,
    dynamic data,
    Map<String, dynamic>? queryParameters,
    T Function(dynamic)? parser,
    BuildContext? context,
    Function(T)? onSuccess,
    Function(ApiError)? onError,
  }) async {
    return request<T>(
      endpoint: endpoint,
      method: HttpMethod.delete,
      data: data,
      queryParameters: queryParameters,
      parser: parser,
      context: context,
      onSuccess: onSuccess,
      onError: onError,
    );
  }
}
