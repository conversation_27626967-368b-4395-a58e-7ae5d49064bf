import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../../../app/extensions/num_ext.dart';
import '../../../app/widgets/app_sized_box.dart';
import '../../../app/widgets/edge_app.dart';
import '../../../app/widgets/g_button.dart';

class CustomTimePicker {
  CustomTimePicker._();

  static Future<DateTime?> pickDate(BuildContext context, DateTime timeStart,
      {DateTime? maximumDate,
      DateTime? minimumDate,
      CupertinoDatePickerMode? mode}) async {
    DateTime timePicked = timeStart;
    Completer<DateTime?> completer = Completer<DateTime?>();
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 320.numH,
          color: Colors.white,
          child: Column(
            children: [
              SizedBox(
                height: 250.numH,
                child: CupertinoDatePicker(
                  initialDateTime: timeStart,
                  minimumDate: minimumDate ?? DateTime(1900),
                  maximumDate: maximumDate ?? DateTime(2200),
                  mode: mode ?? CupertinoDatePickerMode.date,
                  onDateTimeChanged: (DateTime newDate) {
                    timePicked = newDate;
                  },
                ),
              ),
              Padding(
                padding: AppEdge.horizontal16(),
                child: Row(
                  children: [
                    Expanded(
                        child: GButtonBorder(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      title: 'Huỷ',
                    )),
                    AppSizedBox.width8(),
                    Expanded(
                        child: GButton(
                      title: 'Chọn',
                      onPressed: () async {
                        completer.complete(timePicked);
                        Navigator.pop(context);
                      },
                    ))
                  ],
                ),
              ),
              AppSizedBox.height16()
            ],
          ),
        );
      },
    );
    return completer.future;
  }
}
