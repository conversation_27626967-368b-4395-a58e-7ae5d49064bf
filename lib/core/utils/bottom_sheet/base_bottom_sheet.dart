import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../app/extensions/context_extension.dart';
import '../../../app/extensions/num_ext.dart';
import '../../../app/extensions/string_ext.dart';
import '../../../app/extensions/theme_extensions.dart';
import '../../../app/widgets/g_text.dart';

class BaseBottomSheet extends StatelessWidget {
  const BaseBottomSheet({super.key, required this.widget, required this.title});

  final Widget widget;
  final String title;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      onVerticalDragEnd: (DragEndDetails details) =>
          FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        width: double.infinity,
        height: context.screenHeight * 0.9,
        decoration: BoxDecoration(
            color: context.color.kBgSurfaceColor,
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30), topRight: Radius.circular(30))),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Row(
                children: [
                  const Expanded(
                      flex: 1,
                      child: SizedBox(
                        height: 24,
                        width: 24,
                      )),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * 0.7,
                    child: Center(
                      child: GText.bold(
                        title,
                        maxLines: 1,
                        textOverflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Icon(
                          Icons.close,
                          size: 24.numW,
                        )),
                  )
                ],
              ),
            ),
            Expanded(child: widget)
          ],
        ),
      ),
    );
  }
}

class BaseWrapBottomSheet extends StatelessWidget {
  const BaseWrapBottomSheet(
      {super.key, required this.widget, required this.title});

  final Widget widget;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          onVerticalDragEnd: (DragEndDetails details) =>
              FocusManager.instance.primaryFocus?.unfocus(),
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
                color: context.color.kBgSurfaceColor,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30))),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      const Expanded(
                          flex: 1,
                          child: SizedBox(
                            height: 24,
                            width: 24,
                          )),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Center(
                          child: GText.bold(
                            title,
                            maxLines: 1,
                            textOverflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: SvgPicture.asset(
                              'ic_close'.iconSvg,
                              height: 24.numR,
                              width: 24.numR,
                            )),
                      )
                    ],
                  ),
                ),
                Wrap(children: [widget])
              ],
            ),
          ),
        )
      ],
    );
  }
}
