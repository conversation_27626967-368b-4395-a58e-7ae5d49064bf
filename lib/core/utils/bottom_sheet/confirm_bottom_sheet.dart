import 'package:flutter/material.dart';
import '../../../app/constants/font_sizes.dart';
import '../../../app/extensions/theme_extensions.dart';
import '../../../app/widgets/g_button.dart';
import '../../../app/widgets/g_text.dart';
import '../../../core/utils/card_custom_shadow.dart';

class ConfirmBottomSheet extends StatelessWidget {
  const ConfirmBottomSheet(
      this.title, this.message, this.nameAction, this.action,
      {super.key});
  final String title;
  final String message;
  final String nameAction;
  final Function? action;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                  color: context.color.kBgSurfaceColor,
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30))),
              child: Padding(
                padding: const EdgeInsets.only(
                    top: 64, bottom: 36, left: 24, right: 24),
                child: Column(
                  children: [
                    GText.bold(
                      title,
                      fontWeight: FontWeight.w500,
                      fontSize: FontSizes.big24,
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    GText.regular(
                      message,
                      fontWeight: FontWeight.w400,
                      fontSize: FontSizes.medium,
                      color: context.color.kTextErrorColor,
                      maxLines: 10,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    GButton(
                      title: nameAction,
                      textColor: context.color.kBgWhiteColor,
                      radius: 18,
                      padding: const EdgeInsets.only(top: 12, bottom: 12),
                      onPressed: () {
                        action?.call();
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 320,
              child: Column(
                children: [
                  CardCustomShadow(
                    hasShadow: true,
                    child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: context.color.kPrimaryColor,
                          borderRadius: BorderRadius.circular(8),
                        )),
                  )
                ],
              ),
            ),
          ],
        )
      ],
    );
  }
}
