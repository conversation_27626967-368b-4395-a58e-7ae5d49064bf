import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';
import '../../../app/constants/font_sizes.dart';
import '../../../app/extensions/context_extension.dart';
import '../../../app/extensions/num_ext.dart';
import '../../../app/widgets/app_sized_box.dart';
import '../../../app/widgets/edge_app.dart';
import '../../../app/widgets/g_button.dart';
import '../../../app/widgets/g_text.dart';
import '../../core/utils/confirm_dialog.dart';

class DialogUtils {
  static BuildContext? context;

  static void showLoading() {
    context?.loaderOverlay.show();
  }

  static void hideLoading() {
    context?.loaderOverlay.hide();
  }

  Future<String?> showBottomSheetGender({
    required String title,
    required List<String> searchData,
    String? defaultData,
    required BuildContext context,
    required Function(String) result,
  }) async {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return Material(
          child: Container(
            height: (searchData.length * 60 + 130).numH,
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GText.bold(
                  title,
                  fontSize: FontSizes.big,
                ),
                Padding(
                  padding: AppEdge.all10(),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: searchData.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: AppEdge.vertical10(),
                        child: GButtonBorder(
                          onPressed: () {
                            result(searchData[index]);
                            Navigator.pop(context);
                          },
                          title: searchData[index],
                        ),
                      );
                    },
                  ),
                ),
                AppSizedBox.height16()
              ],
            ),
          ),
        );
      },
    );
  }

  Future<String?> showBottomSheetSearch({
    required String title,
    required Widget childWidget,
    required BuildContext context,
  }) async {
    return showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return Material(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: context.screenWidth,
                height: 12.numH,
              ),
              GText.bold(
                title,
                fontSize: FontSizes.big,
              ),
              Padding(
                padding: AppEdge.all10(),
                child: childWidget,
              ),
              AppSizedBox.height10()
            ],
          ),
        );
      },
    );
  }

  static void showDialogMessage(String message,
      {Function? action,
      required BuildContext context,
      String? nameAction,
      String? title}) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => PopScope(
        canPop: false,
        child: Dialog(
          child: DialogMessageWidget(
            title ?? 'common.notification'.tr(),
            message,
            nameAction ?? 'common.close'.tr(),
            action
          ),
        ),
      ),
    );
  }
}
