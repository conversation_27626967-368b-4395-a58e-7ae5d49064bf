import 'package:flutter/material.dart';
import '../../../app/constants/font_sizes.dart';
import '../../../app/extensions/context_extension.dart';
import '../../../app/extensions/num_ext.dart';
import '../../../app/extensions/theme_extensions.dart';
import '../../../app/widgets/edge_app.dart';
import '../../../app/widgets/g_button.dart';
import '../../../app/widgets/g_text.dart';

class DialogMessageWidget extends StatelessWidget {
  const DialogMessageWidget(
      this.title, this.message, this.nameAction, this.action,
      {super.key});
  final String title;
  final String message;
  final String nameAction;
  final Function? action;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        Container(
          width: context.screenWidth * 0.8,
          decoration: BoxDecoration(
              color: context.color.kBgSurfaceColor,
              borderRadius: BorderRadius.circular(20.numR)),
          child: Padding(
            padding: AppEdge.all24(),
            child: Column(
              children: [
                GText.bold(
                  title,
                  fontWeight: FontWeight.w500,
                  fontSize: FontSizes.big18,
                  context: context,
                ),
                SizedBox(
                  height: 32.numH,
                ),
                GText.regular(
                  message,
                  fontWeight: FontWeight.w400,
                  fontSize: FontSizes.medium,
                  color: context.color.kTextBoldSecondColor,
                  maxLines: 10,
                  textAlign: TextAlign.center,
                  context: context,
                ),
                Padding(
                  padding: AppEdge.custom(top: 32),
                  child: SizedBox(
                    width: context.screenWidth * 0.4,
                    child: GButtonSmall(
                      title: nameAction,
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      onPressed: () {
                        if (action == null) {
                          Navigator.pop(context);
                        } else {
                          Navigator.pop(context);
                          action?.call();
                        }
                      },
                    ),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }
}
