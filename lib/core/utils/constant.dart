import 'package:flutter/cupertino.dart';
import '../../app/extensions/num_ext.dart';

final SizedBox sizedBoxHeight4 = SizedBox(
  height: 4.numH,
);

final SizedBox sizedBoxHeight8 = SizedBox(
  height: 8.numH,
);
final SizedBox sizedBoxHeight6 = SizedBox(
  height: 6.numH,
);
final SizedBox sizedBoxHeight12 = SizedBox(
  height: 12.numH,
);
final SizedBox sizedBoxHeight16 = SizedBox(
  height: 16.numH,
);
final SizedBox sizedBoxHeight20 = SizedBox(
  height: 20.numH,
);
final SizedBox sizedBoxHeight24 = SizedBox(
  height: 24.numH,
);
final SizedBox sizedBoxHeight26 = SizedBox(
  height: 26.numH,
);
final SizedBox sizedBoxHeight32 = SizedBox(
  height: 32.numH,
);
final SizedBox sizedBoxHeight48 = SizedBox(
  height: 48.numH,
);

final SizedBox sizedBoxWidth4 = SizedBox(
  width: 4.numW,
);
final SizedBox sizedBoxWidth6 = SizedBox(
  width: 6.numW,
);
final SizedBox sizedBoxWidth8 = SizedBox(
  width: 8.numW,
);
final SizedBox sizedBoxWidth10 = SizedBox(
  width: 10.numW,
);
final SizedBox sizedBoxWidth12 = SizedBox(
  width: 12.numW,
);
final SizedBox sizedBoxWidth14 = SizedBox(
  width: 14.numW,
);
final SizedBox sizedBoxWidth16 = SizedBox(
  width: 16.numW,
);
final SizedBox sizedBoxWidth20 = SizedBox(
  width: 20.numW,
);
final SizedBox sizedBoxWidth24 = SizedBox(
  width: 24.numW,
);
final SizedBox sizedBoxWidth26 = SizedBox(
  width: 26.numW,
);
final SizedBox sizedBoxWidth40 = SizedBox(
  width: 40.numW,
);

enum TypeItemRestaurant { normal, discount, menu }

enum MethodApi { post, put, get, delete }
