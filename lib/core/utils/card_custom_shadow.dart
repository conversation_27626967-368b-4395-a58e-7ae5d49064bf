import 'package:flutter/material.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/theme_extensions.dart';

class CardCustomShadow extends StatelessWidget {
  const CardCustomShadow(
      {super.key,
      required this.child,
      this.padding,
      this.hasShadow,
      this.backgroundColor,
      this.margin,
      this.radius,
      this.borderRadius,
      this.blurRadius});

  final Widget child;
  final EdgeInsetsGeometry? padding;
  final bool? hasShadow;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? margin;
  final double? radius;
  final BorderRadius? borderRadius;
  final double? blurRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.all(16.numW),
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        boxShadow: hasShadow ?? true
            ? [
                CustomBoxShadow(
                  color: context.color.kShadowColor.withValues(alpha: 0.2),
                  blurRadius: blurRadius ?? 10.0.numR,
                  blurStyle: BlurStyle.normal,
                ),
              ]
            : [],
        borderRadius: borderRadius ?? BorderRadius.circular(radius?.numR ?? 10.numR),
      ),
      child: child,
    );
  }
}

class CustomBoxShadow extends BoxShadow {
  const CustomBoxShadow({
    super.color,
    super.offset,
    super.blurRadius,
    BlurStyle blurStyle = BlurStyle.normal,
  }) : _blurStyle = blurStyle;

  final BlurStyle _blurStyle;

  @override
  BlurStyle get blurStyle => _blurStyle;

  @override
  Paint toPaint() {
    final Paint result = Paint()
      ..color = color
      ..maskFilter = MaskFilter.blur(blurStyle, blurSigma);
    assert(() {
      if (debugDisableShadows) {
        result.maskFilter = null;
      }

      return true;
    }());

    return result;
  }
}
