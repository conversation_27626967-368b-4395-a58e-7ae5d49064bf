import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/string_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/edge_app.dart';
import '../../app/widgets/g_inkwell.dart';

enum LeadingType { back, close, drawer, noLeading }

typedef AutoLeadingButtonCustomBuilder = Widget Function(
  BuildContext context,
  LeadingType leadingType,
  VoidCallback? action,
);
class AutoLeadingButtonCustom extends StatelessWidget {
  final Color? color;
  final AutoLeadingButtonCustomBuilder? builder;

  const AutoLeadingButtonCustom({
    super.key,
    this.color,
    this.builder,
  }) : assert(color == null || builder == null);

  @override
  Widget build(BuildContext context) {
    final canPop = GoRouter.of(context).canPop();

    if (canPop) {
      const leadingType = LeadingType.back;

      if (builder != null) {
        return builder!(
          context,
          leadingType,
          () => context.pop(),
        );
      }

      return _buildDefaultBackButton(context);
    }

    final ScaffoldState? scaffold = Scaffold.maybeOf(context);
    if (scaffold?.hasDrawer == true) {
      if (builder != null) {
        return builder!(
          context,
          LeadingType.drawer,
          () => _handleDrawerButton(context),
        );
      }
      return _buildDefaultDrawerButton(context);
    }

    if (builder != null) {
      return builder!(context, LeadingType.noLeading, null);
    }
    return const SizedBox.shrink();
  }

  Widget _buildDefaultBackButton(BuildContext context) {
    return GInkwell.base(
      onTap: () {
        context.pop();
      },
      child: Padding(
        padding: AppEdge.custom(left: 8),
        child: SvgPicture.asset(
          'ic_back'.iconSvg,
          colorFilter: ColorFilter.mode(
            color ?? context.color.kBgBlackColor,
            BlendMode.srcIn,
          ),
          width: 24.numT,
          height: 24.numT,
        ),
      ),
    );
  }

  Widget _buildDefaultDrawerButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.menu),
      iconSize: Theme.of(context).iconTheme.size ?? 24.numT,
      onPressed: () => _handleDrawerButton(context),
      tooltip: MaterialLocalizations.of(context).openAppDrawerTooltip,
    );
  }

  void _handleDrawerButton(BuildContext context) {
    Scaffold.of(context).openDrawer();
  }
}
