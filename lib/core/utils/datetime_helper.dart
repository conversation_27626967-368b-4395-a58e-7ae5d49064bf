// ignore_for_file: constant_identifier_names

import 'package:easy_localization/easy_localization.dart';

enum DateTimeFormat {
  dd_MM_YYYY,
  yyyy_MM_ddTHH_mm_ss,
  yyyy_MM_ddHHmmss,
  yyyyMMddHHmmss,
  yyyy_MM_dd_T_HH_mm_sssZ,
  MM_YYYY,
  YYYY_MM_dd,
  HHmm_ddMMyyyy_splash,
  dd_MMM_yy,
  dd_MM_yyyy_HH_mm_ss
}

extension DateTimeFormatExtension on DateTimeFormat {
  String get getString {
    switch (this) {
      case DateTimeFormat.yyyy_MM_ddTHH_mm_ss:
        return "yyyy-MM-dd'T'HH:mm:ss";
      case DateTimeFormat.dd_MM_YYYY:
        return "dd/MM/yyyy";
      case DateTimeFormat.yyyy_MM_ddHHmmss:
        return "yyyy-MM-dd HH:mm:ss";
      case DateTimeFormat.yyyy_MM_dd_T_HH_mm_sssZ:
        return "yyyy-MM-dd'T'HH:mm:sss.Z";
      case DateTimeFormat.YYYY_MM_dd:
        return "yyyy-MM-dd";
      case DateTimeFormat.MM_YYYY:
        return "MM-dd";
      case DateTimeFormat.yyyyMMddHHmmss:
        return "yyyyMMdd_HHmmss";
      case DateTimeFormat.HHmm_ddMMyyyy_splash:
        return 'HH:mm dd/MM/yyyy';
      case DateTimeFormat.dd_MMM_yy:
        return 'dd/MMM/yy';
      case DateTimeFormat.dd_MM_yyyy_HH_mm_ss:
        return 'dd/MM/yyyy HH:mm:ss';
    }
  }
}

class DateTimeHelper {
  static String dateFormatDDMMYY(DateTime dateTime) {
    DateFormat dateFormat = DateFormat("dd/MM/yyyy");
    return dateFormat.format(dateTime);
  }

  static String dateFormatDDMMYYTZ(DateTime dateTime) {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd");
    return "${dateFormat.format(dateTime)}T17:00:00.000000Z";
  }

  static String dateFormatDDMMYYYYHHMMSS(DateTime dateTime) {
    DateFormat dateFormat = DateFormat("dd/MM/yyy HH:mm:ss");
    return dateFormat.format(dateTime);
  }
}
