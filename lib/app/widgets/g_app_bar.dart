import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_base/app/constants/font_sizes.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/managers/colors_manager.dart';
import '../../app/widgets/g_text.dart';

class GAppBar extends AppBar {
  final String headerTitle;
  final List<Widget>? actionButtons;

  GAppBar(this.headerTitle,
      {super.key, super.bottom, super.elevation = 0.0, this.actionButtons})
      : super(
          toolbarHeight: 60.numH,
          backgroundColor: null,
          systemOverlayStyle: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
          ),
          title: Builder(
            builder: (context) => GText.bold(
              headerTitle,
              context: context,
              fontSize: FontSizes.big18,
            ),
          ),
          actions: [
            if (actionButtons != null) ...actionButtons,
          ],
          shape: const Border(bottom: BorderSide(color: kBorder, width: 0.7)),
          automaticallyImplyLeading: true,
          centerTitle: true,
        );
}
