import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/constants/font_sizes.dart';
import '../../app/extensions/double_ext.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/string_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/edge_app.dart';
import '../../app/widgets/g_inkwell.dart';
import '../../app/widgets/g_text.dart';
import '../../app/widgets/text_field_container.dart';

class RoundedInputField extends StatelessWidget {
  const RoundedInputField(
      {super.key,
      this.textController,
      this.onTextChanged,
      this.onFieldSubmitted,
      required this.hintText,
      this.iconData,
      this.initialValue,
      this.onSuffixTap,
      this.suffixString,
      this.keyboardType,
      this.onSaved,
      this.validator,
      this.suffixIcon,
      this.prefixIcon,
      this.prefixIconColor,
      this.hintTextColor,
      this.textColor,
      this.labelColor,
      this.minLines,
      this.maxLines,
      this.textStyle,
      this.errorText,
      this.enabledBorder,
      this.readOnly,
      this.inputFormatters,
      this.enable,
      this.contentPadding,
      this.onTap,
      this.maxLength,
      this.isFormatCurrency = false,
      this.onTapSuffixIcon});

  final String? suffixString;
  final String? hintText;
  final void Function(String)? onTextChanged;
  final void Function(String)? onFieldSubmitted;
  final IconData? iconData;
  final String? initialValue;
  final String? suffixIcon;
  final String? prefixIcon;
  final TextInputType? keyboardType;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final TextEditingController? textController;
  final Color? hintTextColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? prefixIconColor;
  final int? minLines;
  final int? maxLines;
  final TextStyle? textStyle;
  final String? errorText;
  final InputBorder? enabledBorder;
  final bool? readOnly;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enable;
  final EdgeInsetsGeometry? contentPadding;
  final int? maxLength;
  final bool isFormatCurrency;
  final void Function()? onSuffixTap;
  final void Function()? onTap;
  final void Function()? onTapSuffixIcon;

  @override
  Widget build(BuildContext context) {
    return TextFieldContainer(
      child: TextFormField(
        autovalidateMode: AutovalidateMode.onUserInteraction,
        keyboardType: keyboardType ?? TextInputType.text,
        inputFormatters: isFormatCurrency
            ? [CurrencyTextInputFormatter.currency(symbol: '', locale: 'vi')]
            : inputFormatters,
        controller: textController,
        onSaved: onSaved,
        validator: validator,
        onChanged: onTextChanged,
        onFieldSubmitted: onFieldSubmitted,
        initialValue: initialValue,
        minLines: minLines,
        maxLines: maxLines,
        maxLength: maxLength,
        readOnly: readOnly ?? false,
        enableSuggestions: false,
        autocorrect: false,
        style: textStyle ??
            GoogleFonts.openSans(
              color: textColor ?? context.color.kTextBoldSecondColor,
              fontSize: FontSizes.small.doubleT,
              fontWeight: isFormatCurrency ? FontWeight.w700 : FontWeight.w500,
              decoration: TextDecoration.none,
            ),
        decoration: InputDecoration(
          filled: true,
          fillColor: readOnly == true
              ? context.color.kBgDisableTextFieldColor
              : context.color.kBgGrayColor,
          counterText: '',
          hintText: hintText != null ? hintText! : '',
          errorText: errorText,
          hintStyle: GoogleFonts.openSans(
            color: context.color.kTextBoldThirdColor,
            fontSize: FontSizes.medium.doubleT,
            fontWeight: FontWeight.w400,
          ),
          errorStyle: GoogleFonts.openSans(
            color: context.color.kErrorColor,
            fontSize: FontSizes.small.doubleT,
            fontWeight: FontWeight.w400,
          ),
          errorMaxLines: 5,
          border: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(18.0)),
            borderSide: BorderSide(width: 1, color: context.color.kBgGrayColor),
          ),
          focusedBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              borderRadius: const BorderRadius.all(Radius.circular(18.0))),
          focusedErrorBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              borderRadius: const BorderRadius.all(Radius.circular(18.0))),
          enabledBorder: OutlineInputBorder(
            borderRadius: const BorderRadius.all(Radius.circular(18.0)),
            borderSide: BorderSide(width: 1, color: context.color.kBgGrayColor),
          ),
          errorBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              borderRadius: const BorderRadius.all(Radius.circular(18.0))),
          isDense: false,
          contentPadding: contentPadding ??
              EdgeInsets.symmetric(vertical: 12.numH, horizontal: 16.numW),
          icon: iconData != null
              ? Icon(
                  iconData,
                  color: context.color.kPrimaryColor,
                )
              : null,
          suffixIcon: suffixIcon != null
              ? SvgPicture.asset(
                  suffixIcon!.iconSvg,
                  fit: BoxFit.none,
                  height: 24.numR,
                  width: 24.numR,
                )
              : suffixString != null
                  ? GInkwell.base(
                      onTap: () {
                        onSuffixTap?.call();
                      },
                      child: Padding(
                        padding: AppEdge.custom(right: 16),
                        child: GText.bold(
                          suffixString ?? '',
                          color: context.color.kPrimaryColor,
                        ),
                      ),
                    )
                  : null,
          prefixIcon: prefixIcon != null
              ? Padding(
                  padding: AppEdge.custom(left: 16, right: 8),
                  child: SvgPicture.asset(
                    prefixIcon!.iconSvg,
                    width: 24.numT,
                    height: 24.numT,
                    colorFilter: ColorFilter.mode(
                      prefixIconColor ?? context.color.kPrimaryColor,
                      BlendMode.srcIn,
                    ),
                  ),
                )
              : null,
          enabled: enable ?? true,
        ),
        enabled: enable,
        onTap: onTap,
      ),
    );
  }
}

class TitleRoundedInputField extends StatefulWidget {
  const TitleRoundedInputField(
      {super.key,
      this.label,
      required this.title,
      this.textController,
      this.onTextChanged,
      this.onEditingComplete,
      this.onFieldSubmitted,
      this.hintText,
      this.iconData,
      this.initialValue,
      this.keyboardType,
      this.onSaved,
      this.validator,
      this.suffixIcon,
      this.prefixIcon,
      this.hintTextColor,
      this.textColor,
      this.textTitleColor,
      this.labelColor,
      this.minLines,
      this.maxLines,
      this.textStyle,
      this.errorText,
      this.enabledBorder,
      this.readOnly,
      this.inputFormatters,
      this.enable,
      this.contentPadding,
      this.onTap,
      this.maxLength,
      this.suffixString,
      this.onSuffixTap,
      this.isFormatCurrency = false,
      this.isRequired = false,
      this.isPassword = false});

  final String? hintText;
  final String? label;
  final String? title;
  final void Function(String)? onTextChanged;
  final void Function()? onEditingComplete;
  final void Function(String)? onFieldSubmitted;
  final void Function()? onSuffixTap;
  final IconData? iconData;
  final String? initialValue;
  final String? suffixIcon;
  final String? prefixIcon;
  final String? suffixString;
  final TextInputType? keyboardType;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final TextEditingController? textController;
  final Color? hintTextColor;
  final Color? textColor;
  final Color? textTitleColor;
  final Color? labelColor;
  final int? minLines;
  final int? maxLines;
  final TextStyle? textStyle;
  final String? errorText;
  final InputBorder? enabledBorder;
  final bool? readOnly;
  final List<TextInputFormatter>? inputFormatters;
  final bool? enable;
  final EdgeInsetsGeometry? contentPadding;
  final int? maxLength;
  final bool isFormatCurrency;
  final bool isRequired;
  final bool isPassword;

  final void Function()? onTap;

  @override
  State<TitleRoundedInputField> createState() => _TitleRoundedInputFieldState();
}

class _TitleRoundedInputFieldState extends State<TitleRoundedInputField> {
  bool _isHidden = true;

  String required() {
    if (widget.isRequired) {
      return '(*)';
    }
    return '';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Row(
            children: [
              GText.medium(
                widget.title ?? "",
                color: widget.textTitleColor,
                fontSize: FontSizes.medium,
                lineHeight: 1.55,
                letterSpacing: -0.28, // -2% of 14px = -0.28
                context: context,
              ),
              GText.regular(
                widget.isRequired ? '*' : '',
                color: context.color.kErrorColor,
                fontSize: FontSizes.medium,
                context: context,
              ),
            ],
          ),
        TextFieldContainer(
          hasError: widget.errorText != null,
          child: SizedBox(
            height: 48.numH,
            child: TextFormField(
              textInputAction: (widget.maxLines ?? 0) > 1
                  ? TextInputAction.newline
                  : TextInputAction.done,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              keyboardType: widget.keyboardType ??
                  (widget.isPassword
                      ? TextInputType.visiblePassword
                      : TextInputType.text),
              inputFormatters: widget.isFormatCurrency
                  ? [
                      CurrencyTextInputFormatter.currency(
                          symbol: '', locale: 'vi')
                    ]
                  : widget.inputFormatters,
              controller: widget.textController,
              onSaved: widget.onSaved,
              validator: widget.validator,
              onChanged: widget.onTextChanged,
              onFieldSubmitted: widget.onFieldSubmitted,
              initialValue: widget.initialValue,
              minLines: widget.isPassword ? 1 : widget.minLines,
              maxLines: widget.isPassword ? 1 : widget.maxLines,
              maxLength: widget.maxLength,
              readOnly: widget.readOnly ?? false,
              obscureText: widget.isPassword ? _isHidden : false,
              obscuringCharacter: '*',
              enableSuggestions: !widget.isPassword,
              autocorrect: false,
              style: widget.textStyle ??
                  GoogleFonts.openSans(
                    color: widget.textColor ??
                        context.color.kTextFocusInTextFieldColor,
                    fontSize: FontSizes.small.doubleT,
                    fontWeight: widget.isFormatCurrency
                        ? FontWeight.w700
                        : FontWeight.w500,
                    decoration: TextDecoration.none,
                  ),
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.white,
                counterText: '',
                hintText: widget.hintText != null ? widget.hintText! : '',
                errorText: widget.errorText,
                labelStyle: const TextStyle(decoration: TextDecoration.none),
                hintStyle: GoogleFonts.openSans(
                  color: context.color.kTextBoldThirdColor,
                  fontSize: FontSizes.medium.doubleT,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.none,
                ),
                errorStyle: GoogleFonts.openSans(
                  color: context.color.kErrorColor,
                  fontSize: FontSizes.small.doubleT,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.none,
                ),
                errorMaxLines: 5,
                border: OutlineInputBorder(
                  borderRadius: const BorderRadius.all(Radius.circular(18.0)),
                  borderSide:
                      BorderSide(width: 1, color: context.color.kBgGrayColor),
                ),
                focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        width: 1, color: context.color.kPrimaryColor),
                    borderRadius:
                        const BorderRadius.all(Radius.circular(10.0))),
                focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(width: 1, color: Color(0xFFDF1C41)),
                    borderRadius:
                        const BorderRadius.all(Radius.circular(10.0))),
                enabledBorder: OutlineInputBorder(
                  borderRadius: const BorderRadius.all(Radius.circular(10.0)),
                  borderSide: BorderSide(width: 1, color: Color(0xFFDFE1E7)),
                ),
                errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(width: 1, color: Color(0xFFDF1C41)),
                    borderRadius:
                        const BorderRadius.all(Radius.circular(10.0))),
                isDense: false,
                contentPadding: widget.contentPadding ??
                    EdgeInsets.only(
                        top: 8.numH,
                        right: 12.numW,
                        bottom: 8.numH,
                        left: 12.numW),
                icon: widget.iconData != null
                    ? Icon(
                        widget.iconData,
                        color: context.color.kIconColor,
                      )
                    : null,
                suffixIcon: widget.isPassword
                    ? GestureDetector(
                        onTap: () {
                          setState(() {
                            _isHidden = !_isHidden;
                          });
                        },
                        child: Icon(
                          _isHidden ? Icons.visibility_off : Icons.visibility,
                          color: context.color.kIconColor,
                        ),
                      )
                    : widget.suffixIcon != null
                        ? SvgPicture.asset(
                            widget.suffixIcon!.iconSvg,
                            fit: BoxFit.none,
                            height: 24.numR,
                            width: 24.numR,
                            colorFilter: ColorFilter.mode(
                              context.color.kBgBlackColor,
                              BlendMode.srcIn,
                            ),
                          )
                        : widget.suffixString != null
                            ? GInkwell.base(
                                onTap: () {
                                  widget.onSuffixTap?.call();
                                },
                                child: Padding(
                                  padding: AppEdge.custom(right: 16),
                                  child: GText.bold(
                                    widget.suffixString ?? '',
                                    color: context.color.kPrimaryColor,
                                  ),
                                ),
                              )
                            : null,
                prefixIcon: widget.prefixIcon != null
                    ? Padding(
                        padding: AppEdge.custom(left: 16, right: 8),
                        child: SvgPicture.asset(
                          widget.prefixIcon!.iconSvg,
                          height: 24.numR,
                          width: 24.numR,
                          colorFilter: ColorFilter.mode(
                            context.color.kIconColor,
                            BlendMode.srcIn,
                          ),
                        ),
                      )
                    : null,
                enabled: widget.enable ?? true,
              ),
              enabled: widget.enable,
              onTap: widget.onTap,
            ),
          ),
        ),
      ],
    );
  }
}
