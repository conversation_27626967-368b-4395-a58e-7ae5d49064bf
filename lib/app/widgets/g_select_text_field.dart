import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/constants/font_sizes.dart';
import '../../app/extensions/double_ext.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/string_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/edge_app.dart';
import '../../app/widgets/g_inkwell.dart';
import '../../app/widgets/g_text.dart';
import '../../app/widgets/text_field_container.dart';

class SelectTextField extends StatefulWidget {
  const SelectTextField(
      {super.key,
      this.onChanged,
      required this.hintText,
      this.initialValue,
      this.onSaved,
      this.validator,
      this.controller,
      this.hintTextColor,
      this.textColor,
      this.labelColor,
      this.iconColor,
      this.suffixIcon,
      this.prefixIcon,
      this.textStyle,
      this.errorText,
      this.contentPadding,
      this.inputFormatters,
      this.action,
      this.textTitleColor,
      this.title,
      this.readOnly,
      this.isRequired = false});

  final ValueChanged<String>? onChanged;
  final String? hintText;
  final String? initialValue;
  final String? title;
  final Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final Color? hintTextColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? textTitleColor;
  final IconData? suffixIcon;
  final String? prefixIcon;
  final TextStyle? textStyle;
  final String? errorText;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;
  final Function? action;
  final bool isRequired;
  final bool? readOnly;

  @override
  State<StatefulWidget> createState() => _SelectTextField();
}

class _SelectTextField extends State<SelectTextField> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Row(
            children: [
              GText.regular(
                widget.title ?? "",
                color: widget.textTitleColor,
                fontSize: FontSizes.medium,
                context: context,
              ),
              GText.regular(
                widget.isRequired ? '*' : '',
                color: context.color.kErrorColor,
                fontSize: FontSizes.medium,
                context: context,
              ),
            ],
          ),
        GInkwell.base(
          onTap: () {
            if (widget.readOnly != true) {
              FocusScope.of(context).unfocus();
              widget.action?.call();
            }
          },
          child: IgnorePointer(
            child: TextFieldContainer(
              child: TextFormField(
                minLines: 1,
                maxLines: 3,
                initialValue: widget.initialValue,
                inputFormatters: widget.inputFormatters,
                onSaved: widget.onSaved,
                validator: widget.validator,
                keyboardType: TextInputType.visiblePassword,
                obscuringCharacter: kIsWeb ? '•' : '●',
                onChanged: widget.onChanged,
                style: widget.textStyle ??
                    GoogleFonts.openSans(
                      color: context.color.kTextFocusInTextFieldColor,
                      fontSize: FontSizes.small.doubleT,
                      fontWeight: FontWeight.w400,
                    ),
                controller: widget.controller,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: widget.readOnly == true
                      ? context.color.kBgDisableTextFieldColor
                      : context.color.kBgGrayColor,
                  hintText:
                      widget.hintText != null ? widget.hintText!.tr() : '',
                  errorText: widget.errorText,
                  hintStyle: GoogleFonts.openSans(
                    color: context.color.kTextBoldThirdColor,
                    fontSize: FontSizes.medium.doubleT,
                    fontWeight: FontWeight.w400,
                  ),
                  errorStyle: GoogleFonts.openSans(
                    color: context.color.kTextHelperColor,
                    fontSize: FontSizes.medium.doubleT,
                    fontWeight: FontWeight.w400,
                  ),
                  contentPadding: widget.contentPadding ??
                      EdgeInsets.symmetric(
                          vertical: 12.numH, horizontal: 16.numW),
                  errorMaxLines: 5,
                  border: OutlineInputBorder(
                      borderRadius:
                          const BorderRadius.all(Radius.circular(18.0)),
                      borderSide: BorderSide(
                          width: 1, color: context.color.kBgGrayColor),
                      gapPadding: 8.0),
                  focusedBorder: OutlineInputBorder(
                      gapPadding: 8.0,
                      borderSide: BorderSide(
                          width: 1, color: context.color.kBgGrayColor),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(18.0))),
                  focusedErrorBorder: OutlineInputBorder(
                      gapPadding: 8.0,
                      borderSide: BorderSide(
                          width: 1, color: context.color.kBgGrayColor),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(18.0))),
                  enabledBorder: OutlineInputBorder(
                      borderRadius:
                          const BorderRadius.all(Radius.circular(18.0)),
                      borderSide: BorderSide(
                          width: 1, color: context.color.kBgGrayColor),
                      gapPadding: 8.0),
                  errorBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                          width: 1, color: context.color.kBgGrayColor),
                      gapPadding: 8.0,
                      borderRadius:
                          const BorderRadius.all(Radius.circular(18.0))),
                  suffixIcon: Padding(
                    padding: widget.suffixIcon != null
                        ? AppEdge.custom(right: 16, top: 12, bottom: 12)
                        : AppEdge.custom(right: 8, top: 12, bottom: 12),
                    child: Icon(widget.suffixIcon ?? Icons.calendar_month,
                        size: 24, color: context.color.kIconColor),
                  ),
                  prefixIcon: widget.prefixIcon != null
                      ? SvgPicture.asset(
                          widget.prefixIcon!.iconSvg,
                          width: 24,
                          height: 24,
                          colorFilter: ColorFilter.mode(
                            context.color.kIconColor,
                            BlendMode.srcIn,
                          ),
                        )
                      : null,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class SelectTextFieldNoTitle extends StatefulWidget {
  const SelectTextFieldNoTitle({
    super.key,
    this.onChanged,
    required this.hintText,
    this.initialValue,
    this.onSaved,
    this.validator,
    this.controller,
    this.hintTextColor,
    this.textColor,
    this.labelColor,
    this.iconColor,
    this.suffixIcon,
    this.prefixIcon,
    this.textStyle,
    this.errorText,
    this.contentPadding,
    this.inputFormatters,
    this.action,
    this.textTitleColor,
    this.readOnly,
  });

  final ValueChanged<String>? onChanged;
  final String? hintText;
  final String? initialValue;
  final Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final Color? hintTextColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? textTitleColor;
  final Widget? suffixIcon;
  final String? prefixIcon;
  final TextStyle? textStyle;
  final String? errorText;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;
  final Function? action;
  final bool? readOnly;

  @override
  State<StatefulWidget> createState() => _SelectTextFieldNoTitle();
}

class _SelectTextFieldNoTitle extends State<SelectTextFieldNoTitle> {
  @override
  Widget build(BuildContext context) {
    return GInkwell.base(
      onTap: () {
        if (widget.readOnly != true) {
          FocusScope.of(context).unfocus();
          widget.action?.call();
        }
      },
      child: IgnorePointer(
        child: TextFieldContainer(
          child: TextFormField(
            minLines: 1,
            maxLines: 3,
            initialValue: widget.initialValue,
            inputFormatters: widget.inputFormatters,
            onSaved: widget.onSaved,
            validator: widget.validator,
            keyboardType: TextInputType.visiblePassword,
            obscuringCharacter: kIsWeb ? '•' : '●',
            onChanged: widget.onChanged,
            style: widget.textStyle ??
                GoogleFonts.openSans(
                  color: context.color.kTextFocusInTextFieldColor,
                  fontSize: FontSizes.small.doubleT,
                  fontWeight: FontWeight.w400,
                ),
            controller: widget.controller,
            decoration: InputDecoration(
              filled: true,
              fillColor: widget.readOnly == true
                  ? context.color.kBgDisableTextFieldColor
                  : context.color.kBgGrayColor,
              hintText: widget.hintText != null ? widget.hintText!.tr() : '',
              errorText: widget.errorText,
              hintStyle: GoogleFonts.openSans(
                color: context.color.kTextBoldThirdColor,
                fontSize: FontSizes.medium.doubleT,
                fontWeight: FontWeight.w400,
              ),
              errorStyle: GoogleFonts.openSans(
                color: context.color.kTextHelperColor,
                fontSize: FontSizes.medium.doubleT,
                fontWeight: FontWeight.w400,
              ),
              contentPadding: widget.contentPadding ??
                  EdgeInsets.symmetric(vertical: 12.numH, horizontal: 16.numW),
              errorMaxLines: 5,
              border: OutlineInputBorder(
                  borderRadius: const BorderRadius.all(Radius.circular(18.0)),
                  borderSide:
                      BorderSide(width: 1, color: context.color.kBgGrayColor),
                  gapPadding: 8.0),
              focusedBorder: OutlineInputBorder(
                  gapPadding: 8.0,
                  borderSide:
                      BorderSide(width: 1, color: context.color.kBgGrayColor),
                  borderRadius: const BorderRadius.all(Radius.circular(18.0))),
              focusedErrorBorder: OutlineInputBorder(
                  gapPadding: 8.0,
                  borderSide:
                      BorderSide(width: 1, color: context.color.kBgGrayColor),
                  borderRadius: const BorderRadius.all(Radius.circular(18.0))),
              enabledBorder: OutlineInputBorder(
                  borderRadius: const BorderRadius.all(Radius.circular(18.0)),
                  borderSide:
                      BorderSide(width: 1, color: context.color.kBgGrayColor),
                  gapPadding: 8.0),
              errorBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(width: 1, color: context.color.kBgGrayColor),
                  gapPadding: 8.0,
                  borderRadius: const BorderRadius.all(Radius.circular(18.0))),
              suffixIcon: Padding(
                padding: widget.suffixIcon != null
                    ? AppEdge.custom(right: 16, top: 12, bottom: 12)
                    : AppEdge.custom(right: 8, top: 12, bottom: 12),
                child: SvgPicture.asset(
                    widget.suffixIcon != null
                        ? widget.prefixIcon!.iconSvg
                        : 'ic_down'.iconSvg,
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      context.color.kIconColor,
                      BlendMode.srcIn,
                    )),
              ),
              prefixIcon: widget.prefixIcon != null
                  ? SvgPicture.asset(
                      widget.prefixIcon!.iconSvg,
                      width: 24,
                      height: 24,
                      colorFilter: ColorFilter.mode(
                        context.color.kIconColor,
                        BlendMode.srcIn,
                      ),
                    )
                  : null,
            ),
          ),
        ),
      ),
    );
  }
}
