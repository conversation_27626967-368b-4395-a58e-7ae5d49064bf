import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import '../extensions/theme_extensions.dart';

class LanguageSwitcherButton extends StatelessWidget {
  const LanguageSwitcherButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => _showLanguageDialog(context),
      icon: Icon(
        Icons.language,
        color: context.color.kTextBoldestColor,
      ),
      tooltip: 'language.tooltip'.tr(),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: context.color.kBgSurfaceColor,
          title: Text(
            'language.title'.tr(),
            style: TextStyle(
              color: context.color.kTextBoldestColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildLanguageOption(
                context,
                'language.vietnamese'.tr(),
                '🇻🇳',
                const Locale('vi', 'VN'),
              ),
              const SizedBox(height: 8),
              _buildLanguageOption(
                context,
                'language.english'.tr(),
                '🇺🇸',
                const Locale('en', 'US'),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    String title,
    String flag,
    Locale locale,
  ) {
    final isSelected = context.locale == locale;
    
    return InkWell(
      onTap: () {
        context.setLocale(locale);
        Navigator.of(context).pop();
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? context.color.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? context.color.primary
                : context.color.kBorderColor,
          ),
        ),
        child: Row(
          children: [
            Text(
              flag,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: isSelected
                      ? context.color.primary
                      : context.color.kTextBoldSecondColor,
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                size: 18,
                color: context.color.primary,
              ),
          ],
        ),
      ),
    );
  }
}
