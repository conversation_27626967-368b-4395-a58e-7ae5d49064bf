import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/constants/font_sizes.dart';
import '../../app/extensions/double_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/managers/colors_manager.dart';

class GText extends StatelessWidget {
  final String? text;
  final double? fontSize;
  final Color? color;
  final FontWeight? fontWeight;
  final int? maxLines;
  final TextAlign? textAlign;
  final TextOverflow? textOverflow;
  final double? lineHeight;
  final List<Color>? gradientColors;
  final BuildContext? context;
  final bool isUnderLine;
  final Color? colorUnderline;
  final TextDecoration? decoration;
  final TextStyle? textStyle;
  final double? letterSpacing;

  static Color _getDefaultTextColor(BuildContext? context) {
    return context?.color.kTextBoldestColor ?? kTextBoldest;
  }

  static Color _getDefaultUnderlineColor(BuildContext? context) {
    return context?.color.kTextBoldestColor ?? kTextBlack;
  }

  const GText.base(
    this.text, {
    super.key,
    this.fontSize = FontSizes.medium,
    this.color,
    this.fontWeight,
    this.maxLines,
    this.textAlign,
    this.textOverflow = TextOverflow.ellipsis,
    this.lineHeight,
    this.gradientColors,
    this.context,
    this.letterSpacing,
  })  : isUnderLine = false,
        colorUnderline = null,
        decoration = null,
        textStyle = null;

  @override
  Widget build(BuildContext buildContext) {
    TextStyle baseStyle;

    // Determine base style based on constructor used
    if (textStyle != null) {
      baseStyle = textStyle!;
    } else if (fontWeight == FontWeight.w400) {
      baseStyle = regularFont;
    } else if (fontWeight == FontWeight.w500) {
      baseStyle = mediumFont;
    } else if (fontWeight == FontWeight.w700 || fontWeight == FontWeight.w600) {
      baseStyle = boldFont;
    } else {
      baseStyle = GoogleFonts.openSans();
    }

    final finalStyle = baseStyle.copyWith(
      fontSize: fontSize?.doubleT,
      color: gradientColors != null
          ? Colors.white
          : (color ?? _getDefaultTextColor(context ?? buildContext)),
      fontWeight: fontWeight,
      height: lineHeight,
      letterSpacing: letterSpacing,
      decoration: decoration ?? (isUnderLine ? TextDecoration.underline : null),
      decorationColor: isUnderLine
          ? (colorUnderline ??
              _getDefaultUnderlineColor(context ?? buildContext))
          : null,
      decorationThickness: isUnderLine ? 1 : null,
    );

    final textWidget = Text(
      text ?? '',
      style: finalStyle,
      overflow: textOverflow,
      maxLines: maxLines,
      textAlign: textAlign,
    );

    if (gradientColors != null) {
      return ShaderMask(
        shaderCallback: (bounds) => LinearGradient(
          colors: gradientColors!,
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ).createShader(Rect.fromLTWH(0, 0, bounds.width, bounds.height)),
        child: textWidget,
      );
    }

    return textWidget;
  }

  const GText.regular(
    this.text, {
    super.key,
    this.fontSize = FontSizes.medium,
    this.color,
    this.colorUnderline,
    this.fontWeight = FontWeight.w400,
    this.maxLines,
    this.textAlign,
    this.isUnderLine = false,
    this.textOverflow = TextOverflow.ellipsis,
    this.lineHeight,
    this.gradientColors,
    this.context,
    this.letterSpacing,
  })  : decoration = null,
        textStyle = null;

  const GText.medium(
    this.text, {
    super.key,
    this.fontSize = FontSizes.medium,
    this.color,
    this.colorUnderline,
    this.fontWeight = FontWeight.w500,
    this.maxLines,
    this.textAlign,
    this.isUnderLine = false,
    this.textOverflow = TextOverflow.ellipsis,
    this.lineHeight,
    this.gradientColors,
    this.context,
    this.letterSpacing,
  })  : decoration = null,
        textStyle = null;

  const GText.bold(
    this.text, {
    Key? key,
    this.fontSize = FontSizes.medium,
    this.color,
    this.colorUnderline,
    this.fontWeight = FontWeight.w700,
    this.maxLines,
    this.textAlign,
    this.decoration,
    this.isUnderLine = false,
    this.textStyle,
    this.textOverflow = TextOverflow.ellipsis,
    this.lineHeight,
    this.gradientColors,
    this.context,
    this.letterSpacing,
  }) : super(key: key);
}

final regularFont = GoogleFonts.openSans(
  fontSize: FontSizes.medium.doubleT,
  color: kTextBoldest,
  fontWeight: FontWeight.w400,
);

final mediumFont = GoogleFonts.openSans(
  fontSize: FontSizes.medium,
  color: kTextBoldest,
  fontWeight: FontWeight.w500,
);

final boldFont = GoogleFonts.openSans(
  fontSize: FontSizes.small,
  color: kTextBoldest,
  fontWeight: FontWeight.w600,
);

class GSelectableText extends SelectableText {
  GSelectableText.base(
    String? text, {
    Key? key,
    double? fontSize = FontSizes.medium,
    Color? color = kTextBoldest,
    FontWeight? fontWeight,
    int? maxLines,
    TextAlign? textAlign,
    TextOverflow? textOverflow = TextOverflow.ellipsis,
    double? lineHeight,
  }) : super(
          text ?? '',
          style: GoogleFonts.openSans(
            fontSize: fontSize.doubleT,
            color: color,
            fontWeight: fontWeight,
            height: lineHeight,
          ),
          maxLines: maxLines,
          textAlign: textAlign,
          key: key,
        );

  GSelectableText.medium(
    String? text, {
    Key? key,
    double? fontSize = FontSizes.medium,
    Color? color = kTextBoldest,
    FontWeight? fontWeight = FontWeight.w500,
    int? maxLines,
    TextAlign? textAlign,
    bool isUnderLine = false,
    TextOverflow? textOverflow = TextOverflow.ellipsis,
    double? lineHeight,
  }) : super(
          text ?? '',
          style: mediumFont.copyWith(
              color: color,
              fontSize: fontSize.doubleT,
              fontWeight: fontWeight,
              height: lineHeight,
              decoration: isUnderLine ? TextDecoration.underline : null,
              decorationColor: isUnderLine ? kTextBlack : null,
              decorationThickness: isUnderLine ? 1 : null),
          maxLines: maxLines,
          key: key,
          textAlign: textAlign,
        );

  GSelectableText.bold(
    super.data, {
    super.key,
    double? fontSize = FontSizes.medium,
    Color? color = kTextBoldest,
    FontWeight? fontWeight = FontWeight.w600,
    super.maxLines,
    super.textAlign,
    bool isUnderLine = false,
    TextOverflow? textOverflow = TextOverflow.ellipsis,
    double? lineHeight,
  }) : super(
          style: boldFont.copyWith(
              color: color,
              fontSize: fontSize.doubleT,
              fontWeight: fontWeight,
              height: lineHeight,
              decoration: isUnderLine ? TextDecoration.underline : null,
              decorationColor: isUnderLine ? kTextBlack : null,
              decorationThickness: isUnderLine ? 1 : null),
        );
}
