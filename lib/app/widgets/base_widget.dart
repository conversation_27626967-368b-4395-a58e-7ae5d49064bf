import 'package:flutter/material.dart';
import 'package:flutter_base/app/constants/font_sizes.dart';
import 'package:flutter_base/app/extensions/context_extension.dart';
import 'package:flutter_base/app/extensions/string_ext.dart';
import 'package:flutter_base/app/widgets/edge_app.dart';
import 'package:flutter_base/app/widgets/g_image.dart';
import 'package:flutter_base/app/widgets/g_text.dart';
import '../../app/extensions/double_ext.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/g_app_bar.dart';
import '../../core/cubit/base_state.dart';

class BaseView extends StatelessWidget {
  final AppBar? appBar;
  final CubitStatus? cubitStatus;
  final Widget child;
  final String? titleAppbar;
  final PreferredSizeWidget? bottom;
  final double? elevation;
  final List<Widget>? actionButtons;
  final String? backgroundImagePath;

  const BaseView(
      {super.key,
      this.appBar,
      required this.child,
      this.titleAppbar,
      this.cubitStatus,
      this.bottom,
      this.elevation = 0.0,
      this.actionButtons,
      this.backgroundImagePath});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: appBar ??
            (titleAppbar != null ||
                    bottom != null ||
                    (actionButtons != null && actionButtons!.isNotEmpty)
                ? GAppBar(
                    titleAppbar ?? '',
                    bottom: bottom,
                    elevation: elevation,
                    actionButtons: actionButtons,
                  )
                : null),
        backgroundColor: backgroundImagePath != null
            ? Colors.transparent
            : Theme.of(context).scaffoldBackgroundColor,
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: backgroundImagePath != null
              ? BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(backgroundImagePath!),
                    fit: BoxFit.cover,
                  ),
                )
              : null,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            onVerticalDragEnd: (DragEndDetails details) =>
                FocusManager.instance.primaryFocus?.unfocus(),
            child: SafeArea(child: child),
          ),
        ));
  }
}

class CustomDivider extends StatelessWidget {
  final double? height;
  final Color? color;

  const CustomDivider({super.key, this.height, this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: height.doubleH ?? 0.7.numH,
      color: color ?? context.color.kBorderColor,
    );
  }
}

class BaseViewNoSafeArea extends StatelessWidget {
  final AppBar? appBar;
  final CubitStatus? cubitStatus;
  final Widget child;
  final String? titleAppbar;
  final PreferredSizeWidget? bottom;
  final double? elevation;
  final List<Widget>? actionButtons;
  final String? backgroundImagePath;
  final bool? resizeToAvoidBottomInset;
  final String? authenTitle;

  const BaseViewNoSafeArea(
      {super.key,
      this.appBar,
      required this.child,
      this.titleAppbar,
      this.cubitStatus,
      this.bottom,
      this.elevation = 0.0,
      this.actionButtons,
      this.backgroundImagePath,
      this.authenTitle,
      this.resizeToAvoidBottomInset});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: resizeToAvoidBottomInset ?? true,
        appBar: appBar ??
            (titleAppbar != null ||
                    bottom != null ||
                    (actionButtons != null && actionButtons!.isNotEmpty)
                ? GAppBar(
                    titleAppbar ?? '',
                    bottom: bottom,
                    elevation: elevation,
                    actionButtons: actionButtons,
                  )
                : null),
        backgroundColor: backgroundImagePath != null
            ? Colors.transparent
            : Theme.of(context).scaffoldBackgroundColor,
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: backgroundImagePath != null
              ? BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(backgroundImagePath!),
                    fit: BoxFit.cover,
                  ),
                )
              : null,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            onVerticalDragEnd: (DragEndDetails details) =>
                FocusManager.instance.primaryFocus?.unfocus(),
            child: Column(
              children: [
                authenTitle != null
                    ? Container(
                        margin: AppEdge.custom(
                          top: 60.numH,
                          left: 24.numH,
                          right: 24.numH,
                        ),
                        width: context.screenWidth,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              width: 40.numH,
                              height: 40.numH,
                              decoration: BoxDecoration(
                                color: context.color.kPrimaryColor,
                                borderRadius: BorderRadius.circular(20.numH),
                              ),
                              child: Center(
                                child:
                                    GImage.asset(name: 'chevron-left'.iconImg),
                              ),
                            ),
                            GText.bold(
                              "Forgot Password",
                              fontSize: FontSizes.big18,
                              color: context.color.kTextWhiteColor,
                            ),
                            SizedBox(
                              width: 40.numH,
                              height: 40.numH,
                            )
                          ],
                        ),
                      )
                    : const SizedBox(),
                child,
              ],
            ),
          ),
        ));
  }
}
