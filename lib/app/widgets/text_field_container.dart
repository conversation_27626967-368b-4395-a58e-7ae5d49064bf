import 'package:flutter/material.dart';
import '../../app/extensions/num_ext.dart';

class TextFieldContainer extends StatelessWidget {
  const TextFieldContainer({
    super.key,
    required this.child,
    this.hasError = false,
  });

  final Widget child;
  final bool hasError;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6.numH),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0.numR),
        boxShadow: hasError
            ? [
                BoxShadow(
                  color: Color(0xFFDF1C41).withValues(alpha: 0.24),
                  blurRadius: 0,
                  spreadRadius: 3,
                  offset: Offset(0, 0),
                ),
              ]
            : null,
      ),
      child: child,
    );
  }
}
