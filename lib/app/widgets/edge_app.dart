import 'package:flutter/material.dart';
import '../../app/extensions/double_ext.dart';

class AppEdge extends EdgeInsets {
  AppEdge.vertical4() : super.symmetric(vertical: 4.0.doubleH!);

  AppEdge.vertical6() : super.symmetric(vertical: 6.0.doubleH!);

  AppEdge.vertical8() : super.symmetric(vertical: 8.0.doubleH!);

  AppEdge.vertical10() : super.symmetric(vertical: 10.0.doubleH!);

  AppEdge.vertical12() : super.symmetric(vertical: 12.0.doubleH!);

  AppEdge.vertical16() : super.symmetric(vertical: 16.0.doubleH!);

  AppEdge.horizontal4() : super.symmetric(horizontal: 4.0.doubleW!);

  AppEdge.horizontal6() : super.symmetric(horizontal: 6.0.doubleW!);

  AppEdge.horizontal8() : super.symmetric(horizontal: 8.0.doubleW!);

  AppEdge.horizontal10() : super.symmetric(horizontal: 10.0.doubleW!);

  AppEdge.horizontal12() : super.symmetric(horizontal: 12.0.doubleW!);

  AppEdge.horizontal14() : super.symmetric(horizontal: 14.0.doubleW!);

  AppEdge.horizontal16() : super.symmetric(horizontal: 16.0.doubleW!);

  AppEdge.horizontal24() : super.symmetric(horizontal: 24.0.doubleW!);

  AppEdge.all2()
      : super.only(
            left: 2.0.doubleW!,
            top: 2.0.doubleH!,
            right: 2.0.doubleW!,
            bottom: 2.0.doubleH!);

  AppEdge.all4()
      : super.only(
            left: 4.0.doubleW!,
            top: 4.0.doubleH!,
            right: 4.0.doubleW!,
            bottom: 4.0.doubleH!);

  AppEdge.all6()
      : super.only(
            left: 6.0.doubleW!,
            top: 6.0.doubleH!,
            right: 6.0.doubleW!,
            bottom: 6.0.doubleH!);

  AppEdge.all8()
      : super.only(
            left: 8.0.doubleW!,
            top: 8.0.doubleH!,
            right: 8.0.doubleW!,
            bottom: 8.0.doubleH!);

  AppEdge.all10()
      : super.only(
            left: 10.0.doubleW!,
            top: 10.0.doubleH!,
            right: 10.0.doubleW!,
            bottom: 10.0.doubleH!);

  AppEdge.all12()
      : super.only(
            left: 12.0.doubleW!,
            top: 12.0.doubleH!,
            right: 12.0.doubleW!,
            bottom: 12.0.doubleH!);

  AppEdge.all14()
      : super.only(
            left: 14.0.doubleW!,
            top: 14.0.doubleH!,
            right: 14.0.doubleW!,
            bottom: 14.0.doubleH!);

  AppEdge.all16()
      : super.only(
            left: 16.0.doubleW!,
            top: 16.0.doubleH!,
            right: 16.0.doubleW!,
            bottom: 16.0.doubleH!);

  AppEdge.all24()
      : super.only(
            left: 24.0.doubleW!,
            top: 24.0.doubleH!,
            right: 24.0.doubleW!,
            bottom: 24.0.doubleH!);

  AppEdge.custom(
      {double left = 0.0,
      double top = 0.0,
      double right = 0.0,
      double bottom = 0.0})
      : super.only(
            left: left.doubleW!,
            top: top.doubleW!,
            right: right.doubleW!,
            bottom: bottom.doubleW!);
}
