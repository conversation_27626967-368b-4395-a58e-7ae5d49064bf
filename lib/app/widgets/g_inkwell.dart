import 'package:flutter/cupertino.dart';
import '../../app/extensions/num_ext.dart';

class GInkwell extends CupertinoButton {
  GInkwell.base({
    super.key,
    required Widget child,
    VoidCallback? onTap,
    double padding = 0,
    bool showShadow = false,
    double opacity = 0.4,
    Alignment alignment = Alignment.center,
  }) : super(
          padding: EdgeInsets.all(0.numW),
          alignment: alignment,
          minimumSize: Size(5.numW, 5.numH),
          borderRadius: BorderRadius.circular(0.numR),
          onPressed: onTap,
          child: padding != 0
              ? Padding(
                  padding: EdgeInsets.all(padding.numW),
                  child: child,
                )
              : child,
          pressedOpacity: opacity,
        );
}
