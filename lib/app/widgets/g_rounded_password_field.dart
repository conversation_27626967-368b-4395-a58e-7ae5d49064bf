import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/constants/font_sizes.dart';
import '../../app/extensions/double_ext.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/string_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/edge_app.dart';
import '../../app/widgets/text_field_container.dart';

class RoundedPasswordField extends StatefulWidget {
  const RoundedPasswordField(
      {super.key,
      this.onChanged,
      required this.hintText,
      this.initialValue,
      this.onSaved,
      this.validator,
      this.controller,
      this.hintTextColor,
      this.textColor,
      this.labelColor,
      this.iconColor,
      this.suffixIcon,
      this.prefixIcon,
      this.textStyle,
      this.errorText,
      this.contentPadding,
      this.inputFormatters,
      this.prefixIconColor});

  final ValueChanged<String>? onChanged;
  final String? hintText;
  final String? initialValue;
  final Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final Color? hintTextColor;
  final Color? textColor;
  final Color? labelColor;
  final Color? iconColor;
  final Color? prefixIconColor;
  final Widget? suffixIcon;
  final String? prefixIcon;
  final TextStyle? textStyle;
  final String? errorText;
  final EdgeInsetsGeometry? contentPadding;
  final List<TextInputFormatter>? inputFormatters;

  @override
  State<StatefulWidget> createState() => _RoundedPasswordField();
}

class _RoundedPasswordField extends State<RoundedPasswordField> {
  var _isHidden = true;

  @override
  Widget build(BuildContext context) {
    return TextFieldContainer(
      child: TextFormField(
        initialValue: widget.initialValue,
        inputFormatters: widget.inputFormatters,
        onSaved: widget.onSaved,
        validator: widget.validator,
        keyboardType: TextInputType.visiblePassword,
        obscureText: _isHidden,
        obscuringCharacter: kIsWeb ? '•' : '*',
        onChanged: widget.onChanged,
        style: widget.textStyle ??
            GoogleFonts.inter(
              color: context.color.kTextBoldSecondColor,
              fontSize: FontSizes.medium.doubleT,
              fontWeight: FontWeight.w400,
            ),
        controller: widget.controller,
        decoration: InputDecoration(
          filled: true,
          fillColor: context.color.kBgGrayColor,
          hintText: widget.hintText != null ? widget.hintText! : '',
          errorText: widget.errorText,
          hintStyle: GoogleFonts.inter(
            color: context.color.kTextBoldThirdColor,
            fontSize: FontSizes.medium.doubleT,
            fontWeight: FontWeight.w400,
          ),
          errorStyle: GoogleFonts.inter(
            color: context.color.kErrorColor,
            fontSize: FontSizes.small.doubleT,
            fontWeight: FontWeight.w400,
          ),
          contentPadding: widget.contentPadding ??
              EdgeInsets.symmetric(vertical: 12.numH, horizontal: 16.numW),
          errorMaxLines: 5,
          border: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(18.0)),
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              gapPadding: 8.0),
          focusedBorder: OutlineInputBorder(
              gapPadding: 8.0,
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              borderRadius: const BorderRadius.all(Radius.circular(18.0))),
          focusedErrorBorder: OutlineInputBorder(
              gapPadding: 8.0,
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              borderRadius: const BorderRadius.all(Radius.circular(18.0))),
          enabledBorder: OutlineInputBorder(
              borderRadius: const BorderRadius.all(Radius.circular(18.0)),
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              gapPadding: 8.0),
          errorBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(width: 1, color: context.color.kBgGrayColor),
              gapPadding: 8.0,
              borderRadius: const BorderRadius.all(Radius.circular(18.0))),
          suffixIcon: Material(
            color: Colors.transparent,
            child: Padding(
              padding: AppEdge.custom(right: 8),
              child: widget.suffixIcon ??
                  IconButton(
                    icon: _isHidden
                        ? SvgPicture.asset(
                            'ic_eye'.iconSvg,
                            width: 24.numR,
                            height: 24.numR,
                            colorFilter: ColorFilter.mode(
                              context.color.kIconColor,
                              BlendMode.srcIn,
                            ),
                          )
                        : SvgPicture.asset(
                            'ic_eye_off'.iconSvg,
                            width: 24.numR,
                            height: 24.numR,
                            colorFilter: ColorFilter.mode(
                              context.color.kIconColor,
                              BlendMode.srcIn,
                            ),
                          ),
                    splashRadius: 25,
                    onPressed: () {
                      setState(() {
                        _isHidden = !_isHidden;
                      });
                    },
                  ),
            ),
          ),
          prefixIcon: widget.prefixIcon != null
              ? Padding(
                  padding: AppEdge.custom(left: 16, right: 8),
                  child: SvgPicture.asset(
                    widget.prefixIcon!.iconSvg,
                    width: 24.numR,
                    height: 24.numR,
                    colorFilter: ColorFilter.mode(
                      widget.prefixIconColor ?? context.color.kPrimaryColor,
                      BlendMode.srcIn,
                    ),
                  ),
                )
              : null,
        ),
      ),
    );
  }
}
