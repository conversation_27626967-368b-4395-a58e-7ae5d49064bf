import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../app/constants/font_sizes.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/string_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/edge_app.dart';
import '../../app/widgets/g_image.dart';
import '../../app/widgets/g_text.dart';
import '../../core/utils/constant.dart';

class GButton extends StatelessWidget {
  final String title;
  final String? leftIcon;
  final String? leftImage;
  final bool? isDisable;
  final Color? backgroundColor;
  final Color? textColor;
  final double? radius;
  final double? fontSizeTitle;
  final bool? isLoading;
  final EdgeInsets? padding;

  final VoidCallback? onPressed;

  const GButton(
      {super.key,
      required this.title,
      this.onPressed,
      this.isDisable,
      this.leftIcon,
      this.leftImage,
      this.isLoading,
      this.padding,
      this.backgroundColor,
      this.radius,
      this.textColor,
      this.fontSizeTitle});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 48.numH,
      decoration: BoxDecoration(
        gradient: isDisable == true
            ? null
            : backgroundColor == null
                ? LinearGradient(
                    colors: [Color(0xFF6199FF), Color(0xFF045CFB)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  )
                : null,
        color: isDisable == true ? Color(0xFFCCD9FA) : backgroundColor,
        borderRadius: BorderRadius.circular(radius ?? 100),
        border: Border.all(
          color: isDisable == true ? Color(0xFFCCD9FA) : Color(0xFF4778F5),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF0D0D12).withValues(alpha: 0.06),
            offset: Offset(0, 1),
            blurRadius: 2,
            spreadRadius: 0,
          ),
        ],
      ),
      child: CupertinoButton(
          color: Colors.transparent,
          borderRadius: BorderRadius.all(Radius.circular(radius ?? 100)),
          padding:
              padding ?? AppEdge.custom(top: 8, bottom: 8, left: 16, right: 16),
          pressedOpacity: 0.5,
          onPressed: () {
            if (isDisable != true) {
              onPressed?.call();
            }
            FocusScope.of(context).unfocus();
          },
          child: isLoading == true
              ? Center(
                  child: SizedBox(
                    width: 24.numW,
                    height: 24.numH,
                    child: CircularProgressIndicator(
                      color: textColor ?? Colors.white,
                    ),
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (leftIcon != null)
                      SvgPicture.asset(
                        leftIcon!.iconSvg,
                        width: 24.numW,
                        height: 24.numH,
                      ),
                    if (leftImage != null)
                      GImage.asset(
                        name: leftImage!.iconImg,
                        width: 24.numW,
                        height: 24.numH,
                      ),
                    if (leftIcon != null) sizedBoxWidth8,
                    if (leftImage != null) sizedBoxWidth8,
                    Text(
                      title,
                      style: GoogleFonts.openSans(
                        color: textColor ?? Colors.white,
                        fontSize: fontSizeTitle ?? 16.numT,
                        fontWeight: FontWeight.w600,
                        height: 1.55,
                        letterSpacing: -0.02,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                )),
    );
  }
}

class GButtonSmall extends StatelessWidget {
  final String title;
  final String? leftIcon;
  final String? leftImage;
  final bool? isDisable;
  final Color? backgroundColor;
  final Color? textColor;
  final double? radius;
  final double? fontSizeTitle;

  final EdgeInsets? padding;

  final VoidCallback? onPressed;

  const GButtonSmall(
      {super.key,
      required this.title,
      this.onPressed,
      this.isDisable,
      this.leftIcon,
      this.leftImage,
      this.padding,
      this.backgroundColor,
      this.radius,
      this.textColor,
      this.fontSizeTitle});

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 0,
      borderRadius: BorderRadius.circular(radius ?? 15),
      child: SizedBox(
        child: CupertinoButton(
            color: isDisable == true
                ? context.color.kBgGrayColor
                : backgroundColor ?? context.color.kPrimaryColor,
            borderRadius: BorderRadius.all(Radius.circular(radius ?? 300)),
            padding: padding ??
                AppEdge.custom(top: 12, bottom: 12, left: 16, right: 16),
            pressedOpacity: 0.5,
            onPressed: () {
              if (isDisable != true) {
                onPressed?.call();
              }
              FocusScope.of(context).unfocus();
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (leftIcon != null)
                  SvgPicture.asset(
                    leftIcon!.iconSvg,
                    width: 24,
                    height: 24,
                  ),
                if (leftImage != null)
                  GImage.asset(
                    name: leftImage!.iconImg,
                    width: 24,
                    height: 24,
                  ),
                if (leftIcon != null) sizedBoxWidth8,
                if (leftImage != null) sizedBoxWidth8,
                GText.bold(
                  title,
                  color: textColor ?? context.color.kBgWhiteColor,
                  fontSize: fontSizeTitle ?? 14,
                ),
              ],
            )),
      ),
    );
  }
}

class GButtonBorder extends StatelessWidget {
  final String title;
  final String? leftIcon;
  final bool? isDisable;
  final Color? backgroundColor;
  final Color? borderColor;
  final Color? textColor;
  final double? radius;
  final EdgeInsets? padding;

  final VoidCallback? onPressed;

  const GButtonBorder(
      {super.key,
      required this.title,
      this.onPressed,
      this.isDisable,
      this.leftIcon,
      this.padding,
      this.backgroundColor,
      this.borderColor,
      this.radius,
      this.textColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: borderColor ?? context.color.kPrimaryColor,
        ),
        borderRadius: BorderRadius.circular(radius ?? 300),
      ),
      width: double.infinity,
      child: CupertinoButton(
          color: isDisable == true
              ? context.color.kBgGrayColor
              : backgroundColor ?? context.color.kBgSurfaceColor,
          borderRadius: BorderRadius.circular(radius ?? 300),
          padding: padding ??
              AppEdge.custom(top: 12, bottom: 12, left: 16, right: 16),
          pressedOpacity: 0.5,
          onPressed: () {
            if (isDisable != true) {
              onPressed?.call();
            }
            FocusScope.of(context).unfocus();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (leftIcon != null)
                SvgPicture.asset(
                  leftIcon!.iconSvg,
                  width: 20.numW,
                  height: 20.numH,
                ),
              if (leftIcon != null) sizedBoxWidth8,
              GText.bold(
                title,
                fontSize: FontSizes.medium,
                color: textColor ?? context.color.kPrimaryColor,
                context: context,
              ),
            ],
          )),
    );
  }
}
