import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../extensions/theme_extensions.dart';
import '../managers/theme_manager.dart';

class ThemeSwitcherButton extends StatelessWidget {
  const ThemeSwitcherButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        return IconButton(
          onPressed: () => themeManager.toggleTheme(),
          icon: Icon(
            themeManager.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            color: context.color.kTextBoldestColor,
          ),
        );
      },
    );
  }
}
