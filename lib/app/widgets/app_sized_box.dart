import 'package:flutter/material.dart';
import '../../app/extensions/num_ext.dart';

class AppSizedBox extends SizedBox {
  AppSizedBox.width({
    super.key,
    double width = 16,
  }) : super(width: width.numW);

  AppSizedBox.height({
    super.key,
    double height = 16,
  }) : super(height: height.numH);

  AppSizedBox.height8({
    super.key,
  }) : super(height: 8.numH);
  AppSizedBox.height7_5({
    super.key,
  }) : super(height: 7.5.numH);

  AppSizedBox.height5({
    super.key,
  }) : super(height: 5.numH);

  AppSizedBox.height4({
    super.key,
  }) : super(height: 4.numH);

  AppSizedBox.height10({
    super.key,
  }) : super(height: 10.numH);

  AppSizedBox.height12({
    super.key,
  }) : super(height: 12.numH);

  AppSizedBox.height14({
    super.key,
  }) : super(height: 14.numH);

  AppSizedBox.height16({
    super.key,
  }) : super(height: 16.numH);

  AppSizedBox.height18({
    super.key,
  }) : super(height: 18.numH);

  AppSizedBox.height20({
    super.key,
  }) : super(height: 20.numH);

  AppSizedBox.height24({
    super.key,
  }) : super(height: 24.numH);

  AppSizedBox.height32({
    super.key,
  }) : super(height: 32.numH);

  AppSizedBox.width4({
    super.key,
  }) : super(width: 4.numW);

  AppSizedBox.width6({
    super.key,
  }) : super(width: 6.numW);

  AppSizedBox.width8({
    super.key,
  }) : super(width: 8.numW);

  AppSizedBox.width10({
    super.key,
  }) : super(width: 10.numW);

  AppSizedBox.width12({
    super.key,
  }) : super(width: 12.numW);

  AppSizedBox.width14({
    super.key,
  }) : super(width: 14.numW);

  AppSizedBox.width16({
    super.key,
  }) : super(width: 16.numW);

  AppSizedBox.width18({
    super.key,
  }) : super(width: 18.numW);

  AppSizedBox.width20({
    super.key,
  }) : super(width: 20.numW);

  AppSizedBox.width24({
    super.key,
  }) : super(width: 24.numW);

  AppSizedBox.width28({
    super.key,
  }) : super(width: 28.numW);

  AppSizedBox.width32({
    super.key,
  }) : super(width: 32.numW);
}
