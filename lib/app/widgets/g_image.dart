import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../app/extensions/string_ext.dart';

class GInternetImage extends StatelessWidget {
  const GInternetImage({
    super.key,
    required this.url,
    this.width = 0,
    this.height = 0,
    this.fit = BoxFit.cover,
    this.borderRadius = 0,
    this.placeHolder,
  });

  final String url;
  final double width;
  final double height;
  final double borderRadius;
  final BoxFit fit;
  final String? placeHolder;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: CachedNetworkImage(
        imageUrl: url,
        width: width,
        height: height,
        fit: fit,
        errorWidget: (_, __, ___) {
          return GImage.asset(
            name: 'img_avatar_default'.iconImg,
            height: height,
            width: width,
          );
        },
      ),
    );
  }
}

class GImage extends Image {
  GImage.asset({
    super.key,
    required String name,
    super.width,
    super.height,
    super.color,
    BoxFit boxFit = BoxFit.cover,
  }) : super(
          image: ResizeImage.resizeIfNeeded(
            null,
            null,
            AssetImage(name),
          ),
          fit: boxFit,
        );

  GImage.file({
    super.key,
    required File file,
    super.width,
    super.height,
    super.color,
    BoxFit boxFit = BoxFit.cover,
  }) : super(
          image: ResizeImage.resizeIfNeeded(
            null,
            null,
            FileImage(file),
          ),
          fit: boxFit,
        );
}
