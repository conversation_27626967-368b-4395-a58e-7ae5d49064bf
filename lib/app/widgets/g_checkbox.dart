import 'package:flutter/material.dart';
import '../extensions/num_ext.dart';

class GCheckbox extends StatelessWidget {
  const GCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    this.size = 16.0,
    this.borderRadius = 4.0,
    this.activeColor = const Color(0xFF4778F5),
    this.borderColor = const Color(0xFF4778F5),
    this.checkColor = Colors.white,
    this.borderWidth = 1.0,
  });

  final bool value;
  final ValueChanged<bool?>? onChanged;
  final double size;
  final double borderRadius;
  final Color activeColor;
  final Color borderColor;
  final Color checkColor;
  final double borderWidth;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onChanged?.call(!value),
      child: Container(
        width: size.numW,
        height: size.numH,
        decoration: BoxDecoration(
          color: value ? activeColor : Colors.transparent,
          border: Border.all(
            color: value ? activeColor : borderColor,
            width: borderWidth,
          ),
          borderRadius: BorderRadius.circular(borderRadius.numR),
        ),
        child: value
            ? Icon(
                Icons.check,
                size: (size * 0.7).numR,
                color: checkColor,
              )
            : null,
      ),
    );
  }
}
