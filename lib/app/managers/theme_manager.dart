import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../managers/colors_manager.dart';

enum AppThemeMode { light, dark, system }

class ThemeManager with ChangeNotifier {
  static const String _themeKey = 'app_theme_mode';
  AppThemeMode _themeMode = AppThemeMode.light;
  bool _isSystemDark = false;

  AppThemeMode get themeMode => _themeMode;
  bool get isDarkMode {
    switch (_themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return _isSystemDark;
    }
  }

  ThemeMode get currentTheme {
    switch (_themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  Future<void> init() async {
    await _loadThemeFromPrefs();
    _updateSystemTheme();
  }

  Future<void> _loadThemeFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final themeIndex = prefs.getInt(_themeKey) ?? AppThemeMode.light.index;
    _themeMode = AppThemeMode.values[themeIndex];
  }

  Future<void> _saveThemeToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeKey, _themeMode.index);
  }

  void _updateSystemTheme() {
    final brightness =
        WidgetsBinding.instance.platformDispatcher.platformBrightness;
    _isSystemDark = brightness == Brightness.dark;
  }

  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _saveThemeToPrefs();
      notifyListeners();
    }
  }

  Future<void> toggleTheme() async {
    final newMode = _themeMode == AppThemeMode.light
        ? AppThemeMode.dark
        : AppThemeMode.light;
    await setThemeMode(newMode);
  }

  void updateSystemTheme() {
    final oldSystemDark = _isSystemDark;
    _updateSystemTheme();
    if (oldSystemDark != _isSystemDark && _themeMode == AppThemeMode.system) {
      notifyListeners();
    }
  }

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: kPrimary,
      scaffoldBackgroundColor: kBackground,
      brightness: Brightness.light,
      textTheme: GoogleFonts.openSansTextTheme(),
      appBarTheme: const AppBarTheme(
        backgroundColor: kSurface,
        foregroundColor: kOnSurface,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        iconTheme: IconThemeData(color: kOnSurface),
      ),
      colorScheme: const ColorScheme.light(
        brightness: Brightness.light,
        primary: kPrimary,
        onPrimary: kOnPrimary,
        primaryContainer: kPrimaryContainer,
        onPrimaryContainer: kOnPrimaryContainer,
        secondary: kSecondary,
        onSecondary: kOnSecondary,
        secondaryContainer: kSecondaryContainer,
        onSecondaryContainer: kOnSecondaryContainer,
        tertiary: kWarning,
        onTertiary: kOnWarning,
        tertiaryContainer: kWarningContainer,
        onTertiaryContainer: kOnWarningContainer,
        error: kError,
        onError: kOnError,
        errorContainer: kErrorContainer,
        onErrorContainer: kOnErrorContainer,
        surface: kSurface,
        onSurface: kOnSurface,
        surfaceContainerHighest: kSurfaceVariant,
        onSurfaceVariant: kOnSurfaceVariant,
        outline: kOutline,
        outlineVariant: kOutlineVariant,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        filled: true,
        fillColor: kSurfaceVariant,
        border: OutlineInputBorder(
          borderSide: BorderSide(color: kOutline),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: kOutlineVariant),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: kPrimary, width: 2),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      primaryColor: kPrimaryDark,
      scaffoldBackgroundColor: kBackgroundDark,
      brightness: Brightness.dark,
      textTheme: GoogleFonts.openSansTextTheme(ThemeData.dark().textTheme),
      appBarTheme: const AppBarTheme(
        backgroundColor: kSurfaceDark,
        foregroundColor: kOnSurfaceDark,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        iconTheme: IconThemeData(color: kOnSurfaceDark),
      ),
      colorScheme: const ColorScheme.dark(
        brightness: Brightness.dark,
        primary: kPrimaryDark,
        onPrimary: kOnPrimaryDark,
        primaryContainer: kPrimaryContainerDark,
        onPrimaryContainer: kOnPrimaryContainerDark,
        secondary: kSecondaryDark,
        onSecondary: kOnSecondaryDark,
        secondaryContainer: kSecondaryContainerDark,
        onSecondaryContainer: kOnSecondaryContainerDark,
        tertiary: kWarningDark,
        onTertiary: kOnWarningDark,
        tertiaryContainer: kWarningContainerDark,
        onTertiaryContainer: kOnWarningContainerDark,
        error: kErrorDark,
        onError: kOnErrorDark,
        errorContainer: kErrorContainerDark,
        onErrorContainer: kOnErrorContainerDark,
        surface: kSurfaceDark,
        onSurface: kOnSurfaceDark,
        surfaceContainerHighest: kSurfaceVariantDark,
        onSurfaceVariant: kOnSurfaceVariantDark,
        outline: kOutlineDark,
        outlineVariant: kOutlineVariantDark,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        filled: true,
        fillColor: kSurfaceVariantDark,
        border: OutlineInputBorder(
          borderSide: BorderSide(color: kOutlineDark),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: kOutlineVariantDark),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: kPrimaryDark, width: 2),
        ),
      ),
    );
  }
}
