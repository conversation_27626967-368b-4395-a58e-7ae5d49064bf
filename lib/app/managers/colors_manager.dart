import 'package:flutter/material.dart';

// ============================================================================
// LIGHT THEME COLORS (Material Design 3 inspired)
// ============================================================================

// Primary Colors - Blue based (Professional & Modern)
const kPrimary = Color(0xFF1976D2); // Primary blue
const kPrimaryContainer = Color(0xFFE3F2FD); // Light blue container
const kOnPrimary = Color(0xFFFFFFFF); // White text on primary
const kOnPrimaryContainer = Color(0xFF0D47A1); // Dark blue text on container

// Secondary Colors - Teal accent (Complementary)
const kSecondary = Color(0xFF00796B); // Teal
const kSecondaryContainer = Color(0xFFE0F2F1); // Light teal container
const kOnSecondary = Color(0xFFFFFFFF); // White text on secondary
const kOnSecondaryContainer = Color(0xFF004D40); // Dark teal text on container

// Surface Colors - Light theme
const kSurface = Color(0xFFFFFFFF); // White surface
const kSurfaceVariant = Color(0xFFF5F5F5); // Light grey surface
const kOnSurface = Color(0xFF0D0D12); // Dark text on surface
const kOnSurfaceVariant = Color(0xFF666D80); // Medium text on surface

// Background Colors - Light theme
const kBackground = Color(0xFFFFFBFE); // Slightly warm white
const kOnBackground = Color(0xFF1C1B1F); // Dark text on background

// Error Colors
const kError = Color(0xFFD32F2F); // Material red
const kErrorContainer = Color(0xFFFFEBEE); // Light red container
const kOnError = Color(0xFFFFFFFF); // White text on error
const kOnErrorContainer = Color(0xFFB71C1C); // Dark red text on error container

// Success Colors
const kSuccess = Color(0xFF388E3C); // Material green
const kSuccessContainer = Color(0xFFE8F5E8); // Light green container
const kOnSuccess = Color(0xFFFFFFFF); // White text on success
const kOnSuccessContainer =
    Color(0xFF1B5E20); // Dark green text on success container

// Warning Colors
const kWarning = Color(0xFFF57C00); // Material orange
const kWarningContainer = Color(0xFFFFF3E0); // Light orange container
const kOnWarning = Color(0xFFFFFFFF); // White text on warning
const kOnWarningContainer =
    Color(0xFFE65100); // Dark orange text on warning container

// Outline & Border
const kOutline = Color(0xFF79747E); // Medium grey outline
const kOutlineVariant = Color(0xFFCAC4D0); // Light grey outline

// ============================================================================
// DARK THEME COLORS
// ============================================================================

// Primary Colors - Dark theme
const kPrimaryDark = Color(0xFF90CAF9); // Light blue for dark theme
const kPrimaryContainerDark = Color(0xFF0D47A1); // Dark blue container
const kOnPrimaryDark = Color(0xFF003C8F); // Dark blue text on primary
const kOnPrimaryContainerDark =
    Color(0xFFE3F2FD); // Light blue text on container

// Secondary Colors - Dark theme
const kSecondaryDark = Color(0xFF4DB6AC); // Light teal for dark theme
const kSecondaryContainerDark = Color(0xFF004D40); // Dark teal container
const kOnSecondaryDark = Color(0xFF00251A); // Very dark teal text on secondary
const kOnSecondaryContainerDark =
    Color(0xFFE0F2F1); // Light teal text on container

// Surface Colors - Dark theme
const kSurfaceDark = Color(0xFF121212); // Dark surface
const kSurfaceVariantDark = Color(0xFF2C2C2C); // Medium dark surface
const kOnSurfaceDark = Color(0xFFE6E1E5); // Light text on dark surface
const kOnSurfaceVariantDark =
    Color(0xFFCAC4D0); // Medium light text on dark surface

// Background Colors - Dark theme
const kBackgroundDark = Color(0xFF101010); // Very dark background
const kOnBackgroundDark = Color(0xFFE6E1E5); // Light text on dark background

// Error Colors - Dark theme
const kErrorDark = Color(0xFFEF5350); // Light red for dark theme
const kErrorContainerDark = Color(0xFFB71C1C); // Dark red container
const kOnErrorDark = Color(0xFF690005); // Very dark red text on error
const kOnErrorContainerDark =
    Color(0xFFFFEBEE); // Light red text on error container

// Success Colors - Dark theme
const kSuccessDark = Color(0xFF66BB6A); // Light green for dark theme
const kSuccessContainerDark = Color(0xFF1B5E20); // Dark green container
const kOnSuccessDark = Color(0xFF003300); // Very dark green text on success
const kOnSuccessContainerDark =
    Color(0xFFE8F5E8); // Light green text on success container

// Warning Colors - Dark theme
const kWarningDark = Color(0xFFFFB74D); // Light orange for dark theme
const kWarningContainerDark = Color(0xFFE65100); // Dark orange container
const kOnWarningDark = Color(0xFF663300); // Very dark orange text on warning
const kOnWarningContainerDark =
    Color(0xFFFFF3E0); // Light orange text on warning container

// Outline & Border - Dark theme
const kOutlineDark = Color(0xFF938F99); // Light grey outline for dark theme
const kOutlineVariantDark =
    Color(0xFF49454F); // Medium grey outline for dark theme

// ============================================================================
// LEGACY COLORS (for backward compatibility)
// ============================================================================

const kPrimaryHighEmphasis = Color(0xFFFFFFFF);
const kPrimaryMediumEmphasis = kOnPrimaryContainer;
const kPrimaryDisabled = Color(0xFFB0BEC5);

const kSecondPrimaryHighEmphasis = kOnSecondary;
const kSecondPrimaryMediumEmphasis = kSecondary;
const kSecondPrimaryDisabled = Color(0xFFB0BEC5);

// Background Colors (using new color system)
const kBgSurface = kSurface;
const kBgSurfaceDark = kSurfaceDark;
const kBgError = kErrorContainer;
const kBgSuccess = kSuccessContainer;
const kBgDisable = Color(0x1F000000);
const kBgGray = kSurfaceVariant;
const kBgWhite = kSurface;
const kBgBlack = kSurfaceDark;
const kBgBlackWithOpacity = Color(0x4A000000);
const kBgTextDoing = kPrimaryContainer;
const kBgDisableTextField = Color(0xFFE0E0E0);

const kBgHome = kSurfaceVariant;
const kBgIconLoanProduct = kWarningContainer;
const kBgTextStatus = kSuccessContainer;
const kBgUnSelectedBanner = kWarningContainer;

// Text Colors (using new color system)
const kTextBoldest = kOnSurface; // Darkest text
const kTextBoldSecond = kOnSurfaceVariant; // Medium dark text
const kTextBoldThird = Color(0xFF6B6B6B); // Light dark text
const kTextSuccess = kOnSuccessContainer; // Success text
const kTextError = kOnErrorContainer; // Error text
const kTextDisable = Color(0x61000000); // Disabled text (38% opacity)
const kTextBlack = kOnSurface; // Black text
const kTextWhite = kOnPrimary; // White text
const kTextHelper = Color(0x99000000); // Helper text (60% opacity)
const kTextFocusInTextField = kOnSurface; // Focused text field
const kTextOrange = kOnWarningContainer; // Orange text
const kIcon = Color(0xDE000000); // Icon color (87% opacity)
const kIconContract = kSuccessContainer; // Contract icon background
const kBgIconProfile = kWarningContainer; // Profile icon background
const kBgIconEdit = kWarning; // Edit icon background
const kTextStatus = kOnSuccessContainer; // Status text
const kTextGrey = kOnSurfaceVariant; // Grey text
const kTextGreyBlack = kOnSurface; // Grey-black text
const kBgItemHome = kPrimaryContainer; // Home item background
const kTextCancelLoan = Color(0x4A000000); // Cancel loan text

// Border & Shadow Colors
const kBorder = kOutlineVariant; // Light theme border
const kShadow = Color(0x1F000000); // Light theme shadow

// Dark theme legacy colors (mapped to new system)
const kDarkBackground = kBackgroundDark;
const kDarkSurface = kSurfaceDark;
const kDarkSurfaceVariant = kSurfaceVariantDark;
const kDarkOnSurface = kOnSurfaceDark;
const kDarkOnSurfaceVariant = kOnSurfaceVariantDark;
const kDarkBorder = kOutlineVariantDark;
const kDarkShadow = Color(0x3F000000);

// Additional legacy colors for compatibility
const kFAFED0 = kWarningContainer;
const kFFF5EB = kWarningContainer;
const kD8FFEC = kSuccessContainer;
const kFFEAE9 = kErrorContainer;
const k586F00 = Color(0xFF586F00);

// ColorsManager class for static access
class ColorsManager {
  static const white = kSurface;
  static const black = kOnSurface;
}

Color getCheckboxColor(Set<WidgetState> states) {
  const interactiveStates = <WidgetState>{
    WidgetState.pressed,
    WidgetState.hovered,
    WidgetState.focused,
  };
  if (states.any(interactiveStates.contains)) {
    return kPrimary;
  }

  return kPrimary;
}
