import 'package:flutter/material.dart';
import '../managers/colors_manager.dart';

// Simplified theme extensions using new color system
extension ColorSchemeExtension on ColorScheme {
  // Primary colors
  Color get kPrimaryColor =>
      brightness == Brightness.light ? kPrimary : kPrimaryDark;
  Color get kPrimaryDarkColor => kPrimaryDark;
  Color get kSecondaryColor =>
      brightness == Brightness.light ? kSecondary : kSecondaryDark;
  Color get kSecondaryDarkColor => kSecondaryDark;

  // Error colors
  Color get kErrorColor => brightness == Brightness.light ? kError : kErrorDark;
  Color get kErrorDarkColor => kErrorDark;

  // Success colors
  Color get kSuccessColor =>
      brightness == Brightness.light ? kSuccess : kSuccessDark;
  Color get kSuccessDarkColor => kSuccessDark;

  // Background colors
  Color get kBgSurfaceColor =>
      brightness == Brightness.light ? kSurface : kSurfaceDark;
  Color get kBgSurfaceDarkColor => kSurfaceDark;
  Color get kBgGrayColor =>
      brightness == Brightness.light ? kSurfaceVariant : kSurfaceVariantDark;
  Color get kBgWhiteColor =>
      brightness == Brightness.light ? kSurface : kSurfaceDark;
  Color get kBgBlackColor =>
      brightness == Brightness.light ? kOnSurface : kOnSurfaceDark;
  Color get kBgBlackWithOpacityColor => kBgBlackWithOpacity;
  Color get kBgDisableTextFieldColor => kBgDisableTextField;
  Color get kBgErrorColor =>
      brightness == Brightness.light ? kErrorContainer : kErrorContainerDark;
  Color get kBgSuccessColor => brightness == Brightness.light
      ? kSuccessContainer
      : kSuccessContainerDark;
  Color get kBgDisableColor => kBgDisable;
  Color get kBgHomeColor =>
      brightness == Brightness.light ? kSurfaceVariant : kSurfaceVariantDark;
  Color get kBgIconLoanProductColor => brightness == Brightness.light
      ? kWarningContainer
      : kWarningContainerDark;
  Color get kBgIconProfileColor => brightness == Brightness.light
      ? kWarningContainer
      : kWarningContainerDark;
  Color get kBgUnSelectedBannerColor => brightness == Brightness.light
      ? kWarningContainer
      : kWarningContainerDark;
  Color get kBgIconEditColor =>
      brightness == Brightness.light ? kWarning : kWarningDark;
  Color get kBgTextStatusColor => brightness == Brightness.light
      ? kSuccessContainer
      : kSuccessContainerDark;
  Color get kBgTextDoingColor => brightness == Brightness.light
      ? kPrimaryContainer
      : kPrimaryContainerDark;
  Color get kBgItemHomeColor => brightness == Brightness.light
      ? kPrimaryContainer
      : kPrimaryContainerDark;

  // Text colors
  Color get kTextBoldestColor =>
      brightness == Brightness.light ? kOnSurface : kOnSurfaceDark;
  Color get kTextBoldSecondColor => brightness == Brightness.light
      ? kOnSurfaceVariant
      : kOnSurfaceVariantDark;
  Color get kTextBoldThirdColor => brightness == Brightness.light
      ? const Color(0xFF6B6B6B)
      : const Color(0xFF9E9E9E);
  Color get kTextSuccessColor => brightness == Brightness.light
      ? kOnSuccessContainer
      : kOnSuccessContainerDark;
  Color get kTextErrorColor => brightness == Brightness.light
      ? kOnErrorContainer
      : kOnErrorContainerDark;
  Color get kTextDisableColor => kTextDisable;
  Color get kTextBlackColor =>
      brightness == Brightness.light ? kOnSurface : kOnSurfaceDark;
  Color get kTextWhiteColor =>
      brightness == Brightness.light ? kOnPrimary : kOnPrimaryDark;
  Color get kTextHelperColor => kTextHelper;
  Color get kTextFocusInTextFieldColor =>
      brightness == Brightness.light ? kOnSurface : kOnSurfaceDark;
  Color get kTextOrangeColor => brightness == Brightness.light
      ? kOnWarningContainer
      : kOnWarningContainerDark;
  Color get kTextStatusColor => brightness == Brightness.light
      ? kOnSuccessContainer
      : kOnSuccessContainerDark;
  Color get kTextCancelLoanColor => kTextCancelLoan;
  Color get kTextGrayColor => brightness == Brightness.light
      ? kOnSurfaceVariant
      : kOnSurfaceVariantDark;
  Color get kTextGreyBlackColor =>
      brightness == Brightness.light ? kOnSurface : kOnSurfaceDark;

  // Icon colors
  Color get kIconColor => kIcon;
  Color get kIconContractColor => brightness == Brightness.light
      ? kSuccessContainer
      : kSuccessContainerDark;

  // Border and shadow
  Color get kBorderColor =>
      brightness == Brightness.light ? kOutlineVariant : kOutlineVariantDark;
  Color get kShadowColor => kShadow;

  // Legacy emphasis colors
  Color get kPrimaryHighEmphasisColor => kPrimaryHighEmphasis;
  Color get kPrimaryMediumEmphasisColor => kPrimaryMediumEmphasis;
  Color get kPrimaryDisabledColor => kPrimaryDisabled;
  Color get kSecondPrimaryHighEmphasisColor => kSecondPrimaryHighEmphasis;
  Color get kSecondPrimaryMediumEmphasisColor => kSecondPrimaryMediumEmphasis;
  Color get kSecondPrimaryDisabledColor => kSecondPrimaryDisabled;
}

extension DarkMode on BuildContext {
  bool get isUsingDarkMode {
    final brightness = Theme.of(this).brightness;
    return brightness == Brightness.dark;
  }

  Color adaptiveColor({required Color light, required Color dark}) {
    return isUsingDarkMode ? dark : light;
  }
}

extension GetColor on BuildContext {
  ColorScheme get color => Theme.of(this).colorScheme;
}

// Linear Gradient Colors
class LinearGradientColors {
  static const Color gradientPink = Color(0xFFFF0ADE);
  static const Color gradientLightPink = Color(0xFFF7BDFF);
  static const Color gradientLightGray = Color(0xFFF0F0F0);

  // White to transparent gradient for text
  static const Color gradientWhite = Color(0xFFFFFFFF);
  static const Color gradientWhiteTransparent = Color(0x80FFFFFF);
  static const Color gradientTransparent = Color(0x00FFFFFF);

  // Predefined gradient combinations
  static const List<Color> pinkGradient = [
    gradientPink,
    gradientLightPink,
    gradientLightGray,
  ];

  // White to transparent gradient (for text on dark background)
  static const List<Color> whiteToTransparentGradient = [
    gradientWhite,
    gradientWhiteTransparent,
    gradientTransparent,
  ];
}
