import 'dart:io';
import 'package:flutter/material.dart';
import 'num_ext.dart';

extension ContextExtension on BuildContext {
  double get screenHeight {
    return MediaQuery.of(this).size.height;
  }

  double get screenWidth {
    return MediaQuery.of(this).size.width;
  }

  double get screenHeightNotAppBar {
    return MediaQuery.of(this).size.height - MediaQuery.of(this).padding.top;
  }

  double get heightTabBar {
    if (!Platform.isIOS) return 60.numH;

    int version =
        int.tryParse(Platform.operatingSystemVersion.substring(8, 10)) ?? 0;
    if (version > 15) return 80.numH;
    return 60.numH;
  }
}
