import 'dart:math';

import 'package:flutter/material.dart';

const guidelineBaseWidth = 375;
const guidelineBaseHeight = 812;

final view = WidgetsBinding.instance.platformDispatcher.views.first;
final screenSize = view.physicalSize;
final screenDensity = view.devicePixelRatio;

double get screenWidth => screenSize.width / screenDensity;

double get screenHeight => screenSize.height / screenDensity;

double get scaleWidth => screenWidth / guidelineBaseWidth;

double get scaleHeight => screenHeight / guidelineBaseHeight;

double get scaleText {
  if (screenWidth <= 400) return 1;
  return max(scaleWidth, scaleHeight);
}

double get scaleRadio => min(scaleWidth, scaleHeight);

extension DoubleToStringOrNull on double? {

  String? get doubleToStringOrNull {
    if (this == null) return null;
    return this!.toString();
  }
}

extension SetKeyDouble on double? {
  String? get setKey {
    final random1 = Random().nextInt(100000).hashCode;
    final random2 = Random().nextInt(100000).hashCode;
    if (this == null) return null;
    return 'key_${random1}_$random2';
  }
}

extension DoubleScreenExtension on double? {
  double? get doubleW {
    if (this == null) return null;
    return this! * scaleWidth;
  }

  double? get doubleH {
    if (this == null) return null;
    return this! * scaleHeight;
  }

  double? get doubleT {
    if (this == null) return null;
    return this! * scaleText;
  }

  double? get doubleR {
    if (this == null) return null;
    return this! * scaleRadio;
  }
}