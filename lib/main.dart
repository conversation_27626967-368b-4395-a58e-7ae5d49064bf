import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_base/app/extensions/theme_extensions.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:provider/provider.dart';
import 'app/managers/theme_manager.dart';
import 'app/widgets/system_theme_listener.dart';
import 'core/utils/dialog_utils.dart';
import 'core/routing/app_router.dart';
import 'config/app_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppConfig.initialize();
  await EasyLocalization.ensureInitialized();
  final themeManager = ThemeManager();
  await themeManager.init();
  runApp(EasyLocalization(
    supportedLocales: const [
      Locale("vi", "VN"),
      Locale("en", "US"),
    ],
    fallbackLocale: const Locale("vi", "VN"),
    path: "assets/translations",
    child: ChangeNotifierProvider.value(
      value: themeManager,
      child: const MyApp(),
    ),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    precacheImage(const AssetImage("assets/images/background.png"), context);
    DialogUtils.context = context;

    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        return GlobalLoaderOverlay(
          overlayColor: const Color(0x8A000000),
          overlayWidgetBuilder: (_) => Center(
            child: CircularProgressIndicator(
              color: context.color.kPrimaryColor,
            ),
          ),
          child: ScreenUtilInit(
            designSize: const Size(375, 812),
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (context, widget) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1.0),
                ),
                child: SystemThemeListener(
                  child: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    localizationsDelegates: context.localizationDelegates,
                    supportedLocales: context.supportedLocales,
                    locale: context.locale,
                    title: AppConfig.appName,
                    theme: ThemeManager.lightTheme,
                    darkTheme: ThemeManager.darkTheme,
                    themeMode: themeManager.currentTheme,
                    routerConfig: AppRouter.router,
                  ),
                ),
              );
            }
          ),
        );
      },
    );
  }
}
