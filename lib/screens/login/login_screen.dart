import 'package:flutter/material.dart';
import 'package:flutter_base/app/extensions/string_ext.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../app/extensions/context_extension.dart';
import '../../app/widgets/base_widget.dart';

import './component/fill_data_login.dart';
import './component/logo_login.dart';
import './cubit/login_cubit.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoginCubit()..initData(),
      child: BaseViewNoSafeArea(
        backgroundImagePath: "background".imgPng,
        resizeToAvoidBottomInset: true,
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: context.screenHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  const LogoWidget(),
                  const Spacer(),
                  const FillDataLogin(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
