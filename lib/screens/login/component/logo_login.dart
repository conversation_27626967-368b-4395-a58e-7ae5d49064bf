import 'package:flutter/widgets.dart';
import 'package:flutter_base/app/constants/font_sizes.dart';
import 'package:flutter_base/app/extensions/theme_extensions.dart';
import 'package:flutter_base/app/widgets/app_sized_box.dart';
import 'package:flutter_base/app/widgets/edge_app.dart';
import 'package:flutter_base/app/widgets/g_text.dart';
import '../../../app/extensions/string_ext.dart';
import '../../../app/widgets/g_image.dart';

class LogoWidget extends StatelessWidget {
  const LogoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: AppEdge.custom(
        top: 155,
        left: 24,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GImage.asset(name: "logo".imgPng),
          AppSizedBox.height10(),
          GText.bold(
            "Enjoy your pool more",
            fontSize: FontSizes.big24,
            color: context.color.kTextWhiteColor,
            lineHeight: 1.5,
          ),
          AppSizedBox.height8(),
          Row(
            children: [
              GText.bold(
                "with ",
                fontSize: FontSizes.big24,
                color: context.color.kTextWhiteColor,
                lineHeight: 1.5,
              ),
              GText.bold(
                "Blue Science.",
                fontSize: FontSizes.big24,
                lineHeight: 1.5,
                gradientColors: LinearGradientColors.pinkGradient,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
