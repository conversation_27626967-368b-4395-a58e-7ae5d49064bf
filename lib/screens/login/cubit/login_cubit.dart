import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/networks/api_config.dart';
import '../../../core/networks/api_service.dart';
import '../../../core/utils/dialog_utils.dart';
import '../../../core/routing/route_names.dart';
import '../../../core/preferences/user_preferences_manager.dart';
import '../../../screens/login/cubit/login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(LoginState());
  final ApiService _apiService = ApiService.instance;
  final Prefs _prefs = Prefs.instance;

  void onChangeEmail(String value) {
    emit(state.copyWith(email: value));
  }

  void onChangePassword(String value) {
    emit(state.copyWith(password: value));
  }

  bool validation({required BuildContext context}) {
    if (state.email == null || state.email!.isEmpty) {
      DialogUtils.showDialogMessage(
        "auth.email_required".tr(),
        context: context,
      );
      return false;
    }

    if (state.password == null || state.password!.isEmpty) {
      DialogUtils.showDialogMessage(
        "auth.password_required".tr(),
        context: context,
      );
      return false;
    }
    return true;
  }

  void initData() async {
    final savedEmail = await _prefs.get<String>(Prefs.email);
    if (savedEmail != null) {
      emit(state.copyWith(email: savedEmail));
    }
  }

  void onLogin({required BuildContext context}) async {
    if (!validation(context: context)) return;

    await _apiService.request(
      endpoint: ApiEndpoints.login,
      method: HttpMethod.post,
      data: {
        'url': ApiConfig.baseUrl,
        'email': state.email ?? '',
        'password': state.password ?? '',
      },
      context: context,
      onSuccess: (loginResponse) {
        _prefs.set(Prefs.email, state.email);
        context.go(RoutePaths.home);
      },
      onError: (apiError) {
        String errorMessage = apiError.message;
        DialogUtils.showDialogMessage(
          errorMessage,
          context: context,
        );
      },
    );
  }
}
