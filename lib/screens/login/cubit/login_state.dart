import 'dart:math';
import '../../../core/cubit/base_state.dart';

class LoginState extends BaseState {
  final CubitStatus cubitStatus;
  final String? password;
  final String? email;
  final String? phone;
  final bool? isLoading;
  final int random;

  LoginState({
    this.cubitStatus = CubitStatus.initial,
    this.password,
    this.isLoading = false,
    this.email,
    this.phone,
    this.random = 0,
  });

  @override
  List<Object?> get props => [
        cubitStatus,
        isLoading,
        password,
        email,
        phone,
        random,
      ];

  LoginState copyWith({
    CubitStatus? cubitStatus,
    String? password,
    bool? isLoading,
    String? email,
    String? phone,
  }) {
    return LoginState(
      cubitStatus: cubitStatus ?? this.cubitStatus,
      password: password ?? this.password,
      email: email ?? this.email,
      isLoading: isLoading ?? this.isLoading,
      phone: phone ?? this.phone,
      random: Random().nextInt(10000).hashCode,
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      "name": password,
      "email": email,
      "phone": phone,
    };
  }
}
