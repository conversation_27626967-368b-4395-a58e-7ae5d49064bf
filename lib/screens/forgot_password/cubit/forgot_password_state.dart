import 'dart:math';
import '../../../core/cubit/base_state.dart';

class ForgotPasswordState extends BaseState {
  final CubitStatus cubitStatus;
  final String? password;
  final String? email;
  final String? phone;
  final bool? isLoading;
  final int random;

  ForgotPasswordState({
    this.cubitStatus = CubitStatus.initial,
    this.password,
    this.isLoading = false,
    this.email,
    this.phone,
    this.random = 0,
  });

  @override
  List<Object?> get props => [
        cubitStatus,
        isLoading,
        password,
        email,
        phone,
        random,
      ];

  ForgotPasswordState copyWith({
    CubitStatus? cubitStatus,
    String? password,
    bool? isLoading,
    String? email,
    String? phone,
  }) {
    return ForgotPasswordState(
      cubitStatus: cubitStatus ?? this.cubitStatus,
      password: password ?? this.password,
      email: email ?? this.email,
      isLoading: isLoading ?? this.isLoading,
      phone: phone ?? this.phone,
      random: Random().nextInt(10000).hashCode,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "name": password,
      "email": email,
      "phone": phone,
    };
  }
}
