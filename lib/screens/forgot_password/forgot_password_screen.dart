import 'package:flutter/material.dart';
import 'package:flutter_base/app/extensions/string_ext.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../app/extensions/context_extension.dart';
import '../../app/widgets/base_widget.dart';
import '../login/component/fill_data_login.dart';
import 'cubit/forgot_password_cubit.dart';

class ForgotPassword extends StatelessWidget {
  const ForgotPassword({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ForgotPasswordCubit()..initData(),
      child: BaseViewNoSafeArea(
        backgroundImagePath: "background".imgPng,
        resizeToAvoidBottomInset: true,
        authenTitle: "Forgot Password",
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: context.screenHeight,
            ),
            child: IntrinsicHeight(
              child: <PERSON>umn(
                children: [
                  FillDataLogin(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
