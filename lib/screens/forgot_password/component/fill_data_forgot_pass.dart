import 'package:flutter/material.dart';
import 'package:flutter_base/app/constants/font_sizes.dart';
import 'package:flutter_base/app/extensions/num_ext.dart';
import 'package:flutter_base/app/widgets/g_button.dart';
import 'package:flutter_base/screens/forgot_password/cubit/forgot_password_cubit.dart';
import 'package:flutter_base/screens/forgot_password/cubit/forgot_password_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../app/extensions/context_extension.dart';
import '../../../app/extensions/theme_extensions.dart';
import '../../../app/widgets/app_sized_box.dart';
import '../../../app/widgets/edge_app.dart';
import '../../../app/widgets/g_inkwell.dart';
import '../../../app/widgets/g_rounded_input_field.dart';
import '../../../app/widgets/g_checkbox.dart';
import '../../../app/widgets/g_text.dart';
import '../../../core/routing/route_names.dart';

class FillDataLogin extends StatefulWidget {
  const FillDataLogin({super.key});

  @override
  State<FillDataLogin> createState() => _FillDataLoginState();
}

class _FillDataLoginState extends State<FillDataLogin> {
  bool _rememberMe = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: context.screenWidth,
      decoration: BoxDecoration(
        color: context.color.kTextWhiteColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30.numR),
          topRight: Radius.circular(30.numR),
        ),
      ),
      child: BlocBuilder<ForgotPasswordCubit, ForgotPasswordState>(
          builder: (context, state) {
        final cubit = context.read<ForgotPasswordCubit>();
        return Padding(
          padding: AppEdge.custom(
            left: 24,
            right: 24,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppSizedBox.height32(),
              GText.bold(
                "Enter Your Email",
                fontSize: FontSizes.big24,
                fontWeight: FontWeight.w700,
              ),
              AppSizedBox.height16(),
              TitleRoundedInputField(
                textColor: context.color.kTextBlackColor,
                textTitleColor: context.color.kTextBlackColor,
                title: 'Email',
                hintText: 'Email Address',
                initialValue: state.email,
                onTextChanged: cubit.onChangeEmail,
              ),
              AppSizedBox.height8(),
              TitleRoundedInputField(
                textColor: context.color.kTextBlackColor,
                textTitleColor: context.color.kTextBlackColor,
                title: 'Password',
                hintText: 'Password',
                initialValue: state.password,
                onTextChanged: cubit.onChangePassword,
                isPassword: true,
              ),
              AppSizedBox.height16(),
              Row(
                children: [
                  GCheckbox(
                    size: 16.numH,
                    value: _rememberMe,
                    onChanged: (val) {
                      setState(() {
                        _rememberMe = val ?? false;
                      });
                    },
                  ),
                  AppSizedBox.width12(),
                  GInkwell.base(
                    onTap: () async {},
                    child: GText.regular(
                      "Remember me",
                      color: context.color.kTextGrayColor,
                    ),
                  )
                ],
              ),
              AppSizedBox.height(
                height: 32,
              ),
              GButton(
                title: "Login",
                // isDisable: true,
              ),
              AppSizedBox.height16(),
              Center(
                child: GInkwell.base(
                  onTap: () {
                    context.go(RoutePaths.forgotPassword);
                  },
                  child: GText.base(
                    "Forgot Password?",
                    color: context.color.kPrimaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              AppSizedBox.height(
                height: 50,
              ),
            ],
          ),
        );
      }),
    );
  }
}
