import 'package:flutter/material.dart';
import 'package:flutter_base/app/widgets/base_widget.dart';
import 'package:flutter_base/core/routing/route_names.dart';
import 'package:go_router/go_router.dart';
import '../../app/extensions/num_ext.dart';
import '../../app/extensions/string_ext.dart';
import '../../app/extensions/theme_extensions.dart';
import '../../app/widgets/g_text.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        context.go(RoutePaths.login);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseViewNoSafeArea(
      backgroundImagePath: "splash".imgPng,
      child: Stack(
        children: [
          Positioned(
            top: 342.numH,
            left: 0,
            right: 0,
            child: Image.asset("logo".imgPng),
          ),
          Positioned(
            top: 720.numH,
            left: 0,
            right: 0,
            child: GText.base(
              "version 1.0",
              color: context.color.kTextWhiteColor,
              textAlign: TextAlign.center,
            ),
          )
        ],
      ),
    );
  }
}
