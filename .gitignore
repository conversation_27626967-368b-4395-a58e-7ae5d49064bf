# Miscellaneous
*.class
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Generated files
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart

# Coverage
coverage/
*.lcov

# Environment files
.env
.env.local
.env.development
.env.production
.env.test

# API Keys and secrets
**/secrets.dart
**/api_keys.dart
**/config/secrets/
google-services.json
GoogleService-Info.plist

# Firebase
.firebase/
firebase_options.dart

# Temporary files
*.tmp
*.temp

# Log files
*.log
logs/
crash_reports/

# Cache directories
.cache/
tmp/
temp/

# IDE specific
.vscode/settings.json
*.code-workspace

# Build outputs
*.apk
*.aab
*.ipa
*.app
*.dmg
*.exe
*.msi

# Android specific
*.jks
*.keystore
key.properties
/android/app/upload-keystore.jks
/android/key.properties
/android/app/google-services.json

# iOS specific
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GoogleService-Info.plist

# Generated launcher icons
/android/app/src/main/res/mipmap-*/
/ios/Runner/Assets.xcassets/AppIcon.appiconset/
/web/icons/
/windows/runner/resources/
/macos/Runner/Assets.xcassets/AppIcon.appiconset/

# Generated splash screens
/android/app/src/main/res/drawable/launch_background.xml
/android/app/src/main/res/drawable-*/launch_background.xml
/ios/Runner/Assets.xcassets/LaunchImage.imageset/
/web/splash/

# Auto-generated files
**/generated_plugin_registrant.dart
**/GeneratedPluginRegistrant.swift
**/GeneratedPluginRegistrant.java
**/GeneratedPluginRegistrant.m

# Local development
.local/
local.properties

# Backup files
*.backup
*.bak
*~

# Additional OS files
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if using for web development)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package manager lock files (optional - team decision)
# pubspec.lock
# yarn.lock
# package-lock.json
