name: flutter_base
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.3.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_svg: ^2.2.0
  cached_network_image: ^3.4.1
  google_fonts: ^6.3.0
  currency_text_input_formatter: ^2.3.0
  easy_localization: ^3.0.8
  equatable: ^2.0.7
  dio: ^5.9.0
  loader_overlay: ^5.0.0
  go_router: ^16.1.0
  flutter_bloc: ^9.1.1
  flutter_screenutil: ^5.9.3
  shared_preferences: ^2.3.2
  provider: ^6.1.2
  rename: ^3.1.0
  flutter_launcher_icons: ^0.14.4
  package_info_plus: ^8.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^6.0.0
  flutter_native_splash: ^2.4.6

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo_border.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/logo_border.png"
  windows:
    generate: true
    image_path: "assets/images/logo_border.png"
  macos:
    generate: true
    image_path: "assets/images/logo_border.png"
flutter:
  uses-material-design: true
  assets:
    - assets/icons/
    - assets/images/
    - assets/translations/

flutter_native_splash:
  color: "#012A93"                     

  android: true
  ios: true
  web: false
  fullscreen: true                             
  android_gravity: center               
  ios_content_mode: center                     
  android_12:
    color: "#012A93"
    fullscreen: true
